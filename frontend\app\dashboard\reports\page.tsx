'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  ChartBarIcon,
  DocumentChartBarIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  ArrowDownTrayIcon,
} from '@heroicons/react/24/outline';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
} from 'recharts';
import { dashboardApi } from '@/lib/api';

// Chart Colors
const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];

// Stats Card Component
interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ComponentType<any>;
  color: string;
  description?: string;
}

const StatsCard = ({ title, value, icon: Icon, color, description }: StatsCardProps) => {
  return (
    <div className="card">
      <div className="card-body">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className={`w-12 h-12 ${color} rounded-lg flex items-center justify-center`}>
              <Icon className="w-6 h-6 text-white" />
            </div>
          </div>
          <div className="mr-4 flex-1">
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            {description && (
              <p className="text-sm text-gray-500">{description}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Monthly Sales Chart Component
const MonthlySalesChart = () => {
  const { data: salesResponse, isLoading } = useQuery({
    queryKey: ['monthly-sales', new Date().getFullYear()],
    queryFn: () => dashboardApi.getMonthlySales(new Date().getFullYear()),
  });

  const salesData = salesResponse?.data || [];

  if (isLoading) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">المبيعات الشهرية</h3>
        </div>
        <div className="card-body">
          <div className="flex items-center justify-center h-64">
            <div className="loading-spinner w-8 h-8"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">المبيعات الشهرية</h3>
          <button className="btn btn-secondary btn-sm">
            <ArrowDownTrayIcon className="w-4 h-4 me-2" />
            تصدير
          </button>
        </div>
      </div>
      <div className="card-body">
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={salesData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="monthName" 
              tick={{ fontSize: 12 }}
              interval={0}
              angle={-45}
              textAnchor="end"
              height={60}
            />
            <YAxis tick={{ fontSize: 12 }} />
            <Tooltip 
              formatter={(value: any, name: string) => [
                `${Number(value).toLocaleString('ar-SA')} ريال`,
                name === 'totalAmount' ? 'إجمالي المبيعات' : 'عدد الفواتير'
              ]}
              labelFormatter={(label) => `شهر ${label}`}
            />
            <Bar 
              dataKey="totalAmount" 
              fill="#3b82f6" 
              name="totalAmount"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

// Invoice Status Chart Component
const InvoiceStatusChart = () => {
  const { data: statusResponse, isLoading } = useQuery({
    queryKey: ['invoice-status-stats'],
    queryFn: dashboardApi.getInvoiceStatusStats,
  });

  const statusData = statusResponse?.data || [];

  if (isLoading) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">توزيع حالة الفواتير</h3>
        </div>
        <div className="card-body">
          <div className="flex items-center justify-center h-64">
            <div className="loading-spinner w-8 h-8"></div>
          </div>
        </div>
      </div>
    );
  }

  const pieData = statusData.map((item, index) => ({
    name: item.statusArabic,
    value: item.count,
    color: COLORS[index % COLORS.length],
  }));

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold text-gray-900">توزيع حالة الفواتير</h3>
      </div>
      <div className="card-body">
        <div className="flex flex-col lg:flex-row items-center">
          <div className="w-full lg:w-1/2">
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value: any) => [`${value} فاتورة`, 'العدد']} />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="w-full lg:w-1/2 mt-4 lg:mt-0">
            <div className="space-y-3">
              {statusData.map((item, index) => (
                <div key={item.status} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div 
                      className="w-4 h-4 rounded-full me-3"
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    ></div>
                    <span className="text-sm font-medium text-gray-900">
                      {item.statusArabic}
                    </span>
                  </div>
                  <div className="text-end">
                    <div className="text-sm font-medium text-gray-900">
                      {item.count} فاتورة
                    </div>
                    <div className="text-xs text-gray-500">
                      {item.totalAmount.toLocaleString('ar-SA')} ريال
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Overdue Invoices Component
const OverdueInvoices = () => {
  const { data: overdueResponse, isLoading } = useQuery({
    queryKey: ['overdue-invoices'],
    queryFn: dashboardApi.getOverdueInvoices,
  });

  const overdueInvoices = overdueResponse?.data || [];

  if (isLoading) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">الفواتير المتأخرة</h3>
        </div>
        <div className="card-body">
          <div className="flex items-center justify-center py-8">
            <div className="loading-spinner w-6 h-6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">
            الفواتير المتأخرة ({overdueInvoices.length})
          </h3>
          {overdueInvoices.length > 0 && (
            <button className="btn btn-danger btn-sm">
              إرسال تذكير
            </button>
          )}
        </div>
      </div>
      <div className="card-body">
        {overdueInvoices.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-green-500 mb-2">
              <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <p className="text-gray-600">لا توجد فواتير متأخرة</p>
          </div>
        ) : (
          <div className="space-y-3">
            {overdueInvoices.slice(0, 5).map((invoice) => {
              const daysOverdue = Math.ceil(
                (Date.now() - new Date(invoice.dueDate).getTime()) / (1000 * 60 * 60 * 24)
              );
              
              return (
                <div key={invoice.id} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                  <div>
                    <div className="font-medium text-gray-900">
                      {invoice.invoiceNumber}
                    </div>
                    <div className="text-sm text-gray-600">
                      {invoice.client.name}
                    </div>
                  </div>
                  <div className="text-end">
                    <div className="font-medium text-red-600">
                      {invoice.totalAmount.toLocaleString('ar-SA')} {invoice.currency}
                    </div>
                    <div className="text-sm text-red-500">
                      متأخر {daysOverdue} يوم
                    </div>
                  </div>
                </div>
              );
            })}
            {overdueInvoices.length > 5 && (
              <div className="text-center pt-3">
                <button className="text-sm text-blue-600 hover:text-blue-500">
                  عرض جميع الفواتير المتأخرة ({overdueInvoices.length})
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default function ReportsPage() {
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  // Fetch dashboard stats for summary
  const { data: statsResponse } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: dashboardApi.getStats,
  });

  const stats = statsResponse?.data;

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 5 }, (_, i) => currentYear - i);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">التقارير والإحصائيات</h1>
          <p className="text-gray-600">
            تحليل شامل لأداء الأعمال والمبيعات
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <select
            value={selectedYear}
            onChange={(e) => setSelectedYear(Number(e.target.value))}
            className="form-input"
          >
            {years.map((year) => (
              <option key={year} value={year}>
                {year}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="إجمالي الإيرادات"
          value={`${(stats?.totalRevenue || 0).toLocaleString('ar-SA')} ريال`}
          icon={CurrencyDollarIcon}
          color="bg-green-500"
          description="جميع الفواتير المدفوعة"
        />
        <StatsCard
          title="إيرادات هذا الشهر"
          value={`${(stats?.thisMonthRevenue || 0).toLocaleString('ar-SA')} ريال`}
          icon={ChartBarIcon}
          color="bg-blue-500"
          description="مقارنة بالشهر الماضي"
        />
        <StatsCard
          title="المبلغ المعلق"
          value={`${(stats?.pendingAmount || 0).toLocaleString('ar-SA')} ريال`}
          icon={DocumentChartBarIcon}
          color="bg-yellow-500"
          description="فواتير غير مدفوعة"
        />
        <StatsCard
          title="الفواتير المتأخرة"
          value={stats?.overdueInvoices || 0}
          icon={CalendarIcon}
          color="bg-red-500"
          description="تحتاج متابعة"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <MonthlySalesChart />
        <InvoiceStatusChart />
      </div>

      {/* Overdue Invoices */}
      <OverdueInvoices />

      {/* Export Options */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">تصدير التقارير</h3>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button className="btn btn-secondary">
              <ArrowDownTrayIcon className="w-4 h-4 me-2" />
              تقرير المبيعات (PDF)
            </button>
            <button className="btn btn-secondary">
              <ArrowDownTrayIcon className="w-4 h-4 me-2" />
              تقرير العملاء (Excel)
            </button>
            <button className="btn btn-secondary">
              <ArrowDownTrayIcon className="w-4 h-4 me-2" />
              تقرير الفواتير (CSV)
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
