'use client';

import { useState, useEffect } from 'react';
import { apiClient, type DashboardStats } from '../../lib/api-client';

// Simple Chart Components (without external libraries)
const SimpleBarChart = ({ data, title }: { data: any[], title: string }) => {
  if (!data || data.length === 0) {
    return (
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        overflow: 'hidden'
      }}>
        <div style={{
          padding: '20px 24px',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <h3 style={{
            fontSize: '18px',
            fontWeight: '600',
            color: '#111827',
            margin: 0
          }}>
            {title}
          </h3>
        </div>
        <div style={{ padding: '48px 24px', textAlign: 'center' }}>
          <p style={{ color: '#6b7280' }}>لا توجد بيانات للعرض</p>
        </div>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(item => item.totalAmount || 0));

  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
      overflow: 'hidden'
    }}>
      <div style={{
        padding: '20px 24px',
        borderBottom: '1px solid #e5e7eb'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <h3 style={{
            fontSize: '18px',
            fontWeight: '600',
            color: '#111827',
            margin: 0
          }}>
            {title}
          </h3>
          <button style={{
            display: 'inline-flex',
            alignItems: 'center',
            padding: '8px 16px',
            backgroundColor: '#f3f4f6',
            color: '#374151',
            border: 'none',
            borderRadius: '6px',
            fontSize: '14px',
            cursor: 'pointer'
          }}>
            <span style={{ marginLeft: '8px' }}>📥</span>
            تصدير
          </button>
        </div>
      </div>
      <div style={{ padding: '24px' }}>
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '16px',
          height: '300px'
        }}>
          {data.map((item, index) => {
            const percentage = maxValue > 0 ? (item.totalAmount / maxValue) * 100 : 0;
            return (
              <div key={index} style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px'
              }}>
                <div style={{
                  minWidth: '80px',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151'
                }}>
                  {item.monthName || item.name}
                </div>
                <div style={{
                  flex: 1,
                  height: '24px',
                  backgroundColor: '#f3f4f6',
                  borderRadius: '12px',
                  overflow: 'hidden',
                  position: 'relative'
                }}>
                  <div style={{
                    height: '100%',
                    width: `${percentage}%`,
                    backgroundColor: '#3b82f6',
                    borderRadius: '12px',
                    transition: 'width 0.5s ease-in-out'
                  }}></div>
                </div>
                <div style={{
                  minWidth: '100px',
                  textAlign: 'left',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#111827'
                }}>
                  {(item.totalAmount || 0).toLocaleString('ar-SA')} ريال
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

const SimplePieChart = ({ data, title }: { data: any[], title: string }) => {
  if (!data || data.length === 0) {
    return (
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        overflow: 'hidden'
      }}>
        <div style={{
          padding: '20px 24px',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <h3 style={{
            fontSize: '18px',
            fontWeight: '600',
            color: '#111827',
            margin: 0
          }}>
            {title}
          </h3>
        </div>
        <div style={{ padding: '48px 24px', textAlign: 'center' }}>
          <p style={{ color: '#6b7280' }}>لا توجد بيانات للعرض</p>
        </div>
      </div>
    );
  }

  const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];
  const total = data.reduce((sum, item) => sum + (item.count || 0), 0);

  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
      overflow: 'hidden'
    }}>
      <div style={{
        padding: '20px 24px',
        borderBottom: '1px solid #e5e7eb'
      }}>
        <h3 style={{
          fontSize: '18px',
          fontWeight: '600',
          color: '#111827',
          margin: 0
        }}>
          {title}
        </h3>
      </div>
      <div style={{ padding: '24px' }}>
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '16px'
        }} className="lg:flex-row lg:items-center">
          {/* Simple pie representation */}
          <div style={{
            width: '200px',
            height: '200px',
            borderRadius: '50%',
            background: `conic-gradient(${data.map((item, index) => {
              const percentage = total > 0 ? (item.count / total) * 100 : 0;
              const color = colors[index % colors.length];
              return `${color} 0deg ${percentage * 3.6}deg`;
            }).join(', ')})`,
            margin: '0 auto'
          }} className="lg:mx-0"></div>
          
          {/* Legend */}
          <div style={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            gap: '12px'
          }}>
            {data.map((item, index) => {
              const percentage = total > 0 ? ((item.count / total) * 100).toFixed(1) : '0';
              return (
                <div key={index} style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div style={{
                      width: '16px',
                      height: '16px',
                      backgroundColor: colors[index % colors.length],
                      borderRadius: '4px',
                      marginLeft: '12px'
                    }}></div>
                    <span style={{
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#111827'
                    }}>
                      {item.statusArabic || item.name}
                    </span>
                  </div>
                  <div style={{ textAlign: 'left' }}>
                    <div style={{
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#111827'
                    }}>
                      {item.count} ({percentage}%)
                    </div>
                    <div style={{
                      fontSize: '12px',
                      color: '#6b7280'
                    }}>
                      {(item.totalAmount || 0).toLocaleString('ar-SA')} ريال
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

// Stats Card Component
const StatsCard = ({ title, value, icon, color, description }: {
  title: string;
  value: string | number;
  icon: string;
  color: string;
  description?: string;
}) => {
  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
      overflow: 'hidden'
    }}>
      <div style={{ padding: '24px' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ flexShrink: 0 }}>
            <div style={{
              width: '48px',
              height: '48px',
              backgroundColor: color,
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <span style={{ fontSize: '24px' }}>{icon}</span>
            </div>
          </div>
          <div style={{ marginRight: '16px', flex: 1 }}>
            <p style={{
              fontSize: '14px',
              fontWeight: '500',
              color: '#6b7280',
              margin: '0 0 4px 0'
            }}>
              {title}
            </p>
            <p style={{
              fontSize: '24px',
              fontWeight: 'bold',
              color: '#111827',
              margin: '0 0 4px 0'
            }}>
              {value}
            </p>
            {description && (
              <p style={{
                fontSize: '14px',
                color: '#6b7280',
                margin: 0
              }}>
                {description}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Overdue Invoices Component
const OverdueInvoices = ({ invoices }: { invoices: any[] }) => {
  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
      overflow: 'hidden'
    }}>
      <div style={{
        padding: '20px 24px',
        borderBottom: '1px solid #e5e7eb'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <h3 style={{
            fontSize: '18px',
            fontWeight: '600',
            color: '#111827',
            margin: 0
          }}>
            الفواتير المتأخرة ({invoices.length})
          </h3>
          {invoices.length > 0 && (
            <button style={{
              padding: '8px 16px',
              backgroundColor: '#ef4444',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer'
            }}>
              إرسال تذكير
            </button>
          )}
        </div>
      </div>
      <div style={{ padding: '24px' }}>
        {invoices.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '32px 0' }}>
            <div style={{
              fontSize: '48px',
              color: '#10b981',
              marginBottom: '16px'
            }}>
              ✅
            </div>
            <p style={{ color: '#6b7280' }}>لا توجد فواتير متأخرة</p>
          </div>
        ) : (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '12px'
          }}>
            {invoices.slice(0, 5).map((invoice) => {
              const daysOverdue = Math.ceil(
                (Date.now() - new Date(invoice.dueDate).getTime()) / (1000 * 60 * 60 * 24)
              );
              
              return (
                <div key={invoice.id} style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  padding: '12px',
                  backgroundColor: '#fef2f2',
                  borderRadius: '8px',
                  border: '1px solid #fecaca'
                }}>
                  <div>
                    <div style={{
                      fontWeight: '500',
                      color: '#111827'
                    }}>
                      {invoice.invoiceNumber}
                    </div>
                    <div style={{
                      fontSize: '14px',
                      color: '#6b7280'
                    }}>
                      {invoice.client?.name}
                    </div>
                  </div>
                  <div style={{ textAlign: 'left' }}>
                    <div style={{
                      fontWeight: '500',
                      color: '#dc2626'
                    }}>
                      {invoice.totalAmount?.toLocaleString('ar-SA')} {invoice.currency}
                    </div>
                    <div style={{
                      fontSize: '14px',
                      color: '#dc2626'
                    }}>
                      متأخر {daysOverdue} يوم
                    </div>
                  </div>
                </div>
              );
            })}
            {invoices.length > 5 && (
              <div style={{ textAlign: 'center', paddingTop: '12px' }}>
                <button style={{
                  fontSize: '14px',
                  color: '#2563eb',
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  textDecoration: 'underline'
                }}>
                  عرض جميع الفواتير المتأخرة ({invoices.length})
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default function ReportsPage() {
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 5 }, (_, i) => currentYear - i);

  // Fetch dashboard stats
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await apiClient.getDashboardStats();
        if (response.success) {
          setStats(response.data || null);
        } else {
          setError(response.message);
        }
      } catch (err: any) {
        setError(err.message || 'حدث خطأ في تحميل البيانات');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [selectedYear]);

  if (loading) {
    return (
      <div style={{
        fontFamily: 'Cairo, Arial, sans-serif',
        direction: 'rtl'
      }}>
        <div style={{
          minHeight: '400px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{
              width: '32px',
              height: '32px',
              border: '2px solid #e5e7eb',
              borderTop: '2px solid #3b82f6',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              margin: '0 auto 16px'
            }}></div>
            <p style={{ color: '#6b7280' }}>جاري تحميل التقارير...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        fontFamily: 'Cairo, Arial, sans-serif',
        direction: 'rtl'
      }}>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
          <h1 style={{
            fontSize: '28px',
            fontWeight: 'bold',
            color: '#111827',
            margin: 0
          }}>
            التقارير والإحصائيات
          </h1>
          
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
            overflow: 'hidden'
          }}>
            <div style={{ padding: '48px 24px', textAlign: 'center' }}>
              <div style={{ fontSize: '48px', color: '#ef4444', marginBottom: '16px' }}>⚠️</div>
              <h3 style={{
                fontSize: '18px',
                fontWeight: '500',
                color: '#111827',
                marginBottom: '8px'
              }}>
                خطأ في تحميل البيانات
              </h3>
              <p style={{
                color: '#6b7280',
                marginBottom: '16px'
              }}>
                {error}
              </p>
              <button
                onClick={() => window.location.reload()}
                style={{
                  padding: '12px 24px',
                  backgroundColor: '#2563eb',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}
              >
                إعادة المحاولة
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Mock data for charts (since we don't have monthly sales endpoint)
  const monthlySalesData = [
    { monthName: 'يناير', totalAmount: 15000 },
    { monthName: 'فبراير', totalAmount: 18000 },
    { monthName: 'مارس', totalAmount: 22000 },
    { monthName: 'أبريل', totalAmount: 19000 },
    { monthName: 'مايو', totalAmount: 25000 },
    { monthName: 'يونيو', totalAmount: 28000 },
  ];

  const statusData = [
    { statusArabic: 'مدفوعة', count: stats?.paidInvoices || 0, totalAmount: stats?.totalRevenue || 0 },
    { statusArabic: 'مرسلة', count: (stats?.totalInvoices || 0) - (stats?.paidInvoices || 0), totalAmount: stats?.pendingAmount || 0 },
    { statusArabic: 'متأخرة', count: stats?.overdueInvoices || 0, totalAmount: 0 },
  ];

  const overdueInvoices = stats?.recentInvoices?.filter(invoice => 
    invoice.status === 'SENT' && new Date(invoice.dueDate) < new Date()
  ) || [];

  return (
    <div style={{
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: 'rtl'
    }}>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '8px'
        }} className="sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 style={{
              fontSize: '28px',
              fontWeight: 'bold',
              color: '#111827',
              margin: '0 0 4px 0'
            }}>
              التقارير والإحصائيات
            </h1>
            <p style={{
              color: '#6b7280',
              margin: 0
            }}>
              تحليل شامل لأداء الأعمال والمبيعات
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <select
              value={selectedYear}
              onChange={(e) => setSelectedYear(Number(e.target.value))}
              style={{
                padding: '12px 16px',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '14px',
                outline: 'none',
                backgroundColor: 'white'
              }}
            >
              {years.map((year) => (
                <option key={year} value={year}>
                  {year}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Summary Stats */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '24px'
        }}>
          <StatsCard
            title="إجمالي الإيرادات"
            value={`${(stats?.totalRevenue || 0).toLocaleString('ar-SA')} ريال`}
            icon="💰"
            color="#dcfce7"
            description="جميع الفواتير المدفوعة"
          />
          <StatsCard
            title="إيرادات هذا الشهر"
            value={`${(stats?.thisMonthRevenue || 0).toLocaleString('ar-SA')} ريال`}
            icon="📈"
            color="#dbeafe"
            description="مقارنة بالشهر الماضي"
          />
          <StatsCard
            title="المبلغ المعلق"
            value={`${(stats?.pendingAmount || 0).toLocaleString('ar-SA')} ريال`}
            icon="⏳"
            color="#fef3c7"
            description="فواتير غير مدفوعة"
          />
          <StatsCard
            title="الفواتير المتأخرة"
            value={stats?.overdueInvoices || 0}
            icon="⚠️"
            color="#fee2e2"
            description="تحتاج متابعة"
          />
        </div>

        {/* Charts */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr',
          gap: '24px'
        }} className="lg:grid-cols-2">
          <SimpleBarChart data={monthlySalesData} title="المبيعات الشهرية" />
          <SimplePieChart data={statusData} title="توزيع حالة الفواتير" />
        </div>

        {/* Overdue Invoices */}
        <OverdueInvoices invoices={overdueInvoices} />

        {/* Export Options */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
          overflow: 'hidden'
        }}>
          <div style={{
            padding: '20px 24px',
            borderBottom: '1px solid #e5e7eb'
          }}>
            <h3 style={{
              fontSize: '18px',
              fontWeight: '600',
              color: '#111827',
              margin: 0
            }}>
              تصدير التقارير
            </h3>
          </div>
          <div style={{ padding: '24px' }}>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '16px'
            }}>
              <button style={{
                display: 'inline-flex',
                alignItems: 'center',
                padding: '12px 24px',
                backgroundColor: '#f3f4f6',
                color: '#374151',
                border: 'none',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '500',
                cursor: 'pointer'
              }}>
                <span style={{ marginLeft: '8px' }}>📄</span>
                تقرير المبيعات (PDF)
              </button>
              <button style={{
                display: 'inline-flex',
                alignItems: 'center',
                padding: '12px 24px',
                backgroundColor: '#f3f4f6',
                color: '#374151',
                border: 'none',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '500',
                cursor: 'pointer'
              }}>
                <span style={{ marginLeft: '8px' }}>📊</span>
                تقرير العملاء (Excel)
              </button>
              <button style={{
                display: 'inline-flex',
                alignItems: 'center',
                padding: '12px 24px',
                backgroundColor: '#f3f4f6',
                color: '#374151',
                border: 'none',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '500',
                cursor: 'pointer'
              }}>
                <span style={{ marginLeft: '8px' }}>📋</span>
                تقرير الفواتير (CSV)
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Add CSS animation */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}
