'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth, useAuthActions } from '../../store/auth-store';

export default function LoginPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading, error } = useAuth();
  const { login, clearError } = useAuthActions();
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    email: '<EMAIL>',
    password: 'admin123'
  });
  const [formErrors, setFormErrors] = useState<any>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, router]);

  // Clear error when component mounts
  useEffect(() => {
    clearError();
  }, [clearError]);

  const validateForm = () => {
    const errors: any = {};

    if (!formData.email) {
      errors.email = 'البريد الإلكتروني مطلوب';
    } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(formData.email)) {
      errors.email = 'صيغة البريد الإلكتروني غير صحيحة';
    }

    if (!formData.password) {
      errors.password = 'كلمة المرور مطلوبة';
    } else if (formData.password.length < 6) {
      errors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    const success = await login(formData.email, formData.password);
    setIsSubmitting(false);

    if (success) {
      router.push('/dashboard');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error for this field
    if (formErrors[name]) {
      setFormErrors((prev: any) => ({ ...prev, [name]: '' }));
    }
  };

  const fillDemoCredentials = () => {
    setFormData({
      email: '<EMAIL>',
      password: 'admin123'
    });
    setFormErrors({});
  };

  if (isAuthenticated) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontFamily: 'Cairo, Arial, sans-serif'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '32px',
            height: '32px',
            border: '2px solid #e5e7eb',
            borderTop: '2px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <p style={{ color: '#6b7280' }}>جاري التحويل...</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%)',
      padding: '48px 16px',
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: 'rtl'
    }}>
      <div style={{ maxWidth: '400px', width: '100%' }}>
        {/* Header */}
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <div style={{
            width: '64px',
            height: '64px',
            background: 'linear-gradient(135deg, #2563eb 0%, #4f46e5 100%)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 24px',
            boxShadow: '0 4px 12px rgba(37, 99, 235, 0.3)'
          }}>
            <svg
              style={{ width: '32px', height: '32px', color: 'white' }}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
          </div>
          <h2 style={{
            fontSize: '28px',
            fontWeight: 'bold',
            color: '#111827',
            marginBottom: '8px'
          }}>
            نظام إدارة الفواتير
          </h2>
          <p style={{ color: '#6b7280', fontSize: '16px' }}>
            سجل دخولك للوصول إلى لوحة التحكم
          </p>
        </div>

        {/* Login Form */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
          overflow: 'hidden'
        }}>
          <div style={{ padding: '32px' }}>
            <form onSubmit={handleSubmit}>
              {/* Error Message */}
              {error && (
                <div style={{
                  backgroundColor: '#fef2f2',
                  border: '1px solid #fecaca',
                  borderRadius: '8px',
                  padding: '16px',
                  marginBottom: '24px'
                }}>
                  <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                    <div style={{ flexShrink: 0 }}>
                      <svg
                        style={{ width: '20px', height: '20px', color: '#f87171' }}
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div style={{ marginRight: '12px' }}>
                      <p style={{ fontSize: '14px', color: '#991b1b' }}>{error}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Email Field */}
              <div style={{ marginBottom: '24px' }}>
                <label htmlFor="email" style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '8px'
                }}>
                  البريد الإلكتروني
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: `1px solid ${formErrors.email ? '#f87171' : '#d1d5db'}`,
                    borderRadius: '8px',
                    fontSize: '16px',
                    outline: 'none',
                    transition: 'border-color 0.2s',
                    backgroundColor: formErrors.email ? '#fef2f2' : 'white'
                  }}
                  placeholder="أدخل بريدك الإلكتروني"
                  onFocus={(e) => {
                    e.target.style.borderColor = '#3b82f6';
                    e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = formErrors.email ? '#f87171' : '#d1d5db';
                    e.target.style.boxShadow = 'none';
                  }}
                />
                {formErrors.email && (
                  <p style={{
                    marginTop: '4px',
                    fontSize: '14px',
                    color: '#dc2626'
                  }}>
                    {formErrors.email}
                  </p>
                )}
              </div>

              {/* Password Field */}
              <div style={{ marginBottom: '24px' }}>
                <label htmlFor="password" style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '8px'
                }}>
                  كلمة المرور
                </label>
                <div style={{ position: 'relative' }}>
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="current-password"
                    value={formData.password}
                    onChange={handleInputChange}
                    style={{
                      width: '100%',
                      padding: '12px 48px 12px 16px',
                      border: `1px solid ${formErrors.password ? '#f87171' : '#d1d5db'}`,
                      borderRadius: '8px',
                      fontSize: '16px',
                      outline: 'none',
                      transition: 'border-color 0.2s',
                      backgroundColor: formErrors.password ? '#fef2f2' : 'white'
                    }}
                    placeholder="أدخل كلمة المرور"
                    onFocus={(e) => {
                      e.target.style.borderColor = '#3b82f6';
                      e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = formErrors.password ? '#f87171' : '#d1d5db';
                      e.target.style.boxShadow = 'none';
                    }}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    style={{
                      position: 'absolute',
                      left: '12px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      background: 'none',
                      border: 'none',
                      cursor: 'pointer',
                      color: '#9ca3af',
                      padding: '4px'
                    }}
                  >
                    {showPassword ? '🙈' : '👁️'}
                  </button>
                </div>
                {formErrors.password && (
                  <p style={{
                    marginTop: '4px',
                    fontSize: '14px',
                    color: '#dc2626'
                  }}>
                    {formErrors.password}
                  </p>
                )}
              </div>

              {/* Demo Credentials */}
              <div style={{
                backgroundColor: '#dbeafe',
                border: '1px solid #93c5fd',
                borderRadius: '8px',
                padding: '16px',
                marginBottom: '24px'
              }}>
                <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                  <div style={{ flexShrink: 0 }}>
                    <svg
                      style={{ width: '20px', height: '20px', color: '#60a5fa' }}
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div style={{ marginRight: '12px', flex: 1 }}>
                    <h3 style={{
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#1e40af',
                      marginBottom: '4px'
                    }}>
                      حساب تجريبي
                    </h3>
                    <p style={{
                      fontSize: '14px',
                      color: '#1d4ed8',
                      marginBottom: '8px'
                    }}>
                      يمكنك استخدام الحساب التجريبي للتجربة
                    </p>
                    <button
                      type="button"
                      onClick={fillDemoCredentials}
                      style={{
                        fontSize: '14px',
                        color: '#2563eb',
                        fontWeight: '500',
                        background: 'none',
                        border: 'none',
                        cursor: 'pointer',
                        textDecoration: 'underline'
                      }}
                      onMouseOver={(e) => e.target.style.color = '#1d4ed8'}
                      onMouseOut={(e) => e.target.style.color = '#2563eb'}
                    >
                      استخدام البيانات التجريبية
                    </button>
                  </div>
                </div>
              </div>

              {/* Submit Button */}
              <div style={{ marginBottom: '24px' }}>
                <button
                  type="submit"
                  disabled={isSubmitting || isLoading}
                  style={{
                    width: '100%',
                    padding: '14px 24px',
                    backgroundColor: (isSubmitting || isLoading) ? '#9ca3af' : '#2563eb',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontSize: '16px',
                    fontWeight: '500',
                    cursor: (isSubmitting || isLoading) ? 'not-allowed' : 'pointer',
                    transition: 'background-color 0.2s',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                  onMouseOver={(e) => {
                    if (!isSubmitting && !isLoading) {
                      e.target.style.backgroundColor = '#1d4ed8';
                    }
                  }}
                  onMouseOut={(e) => {
                    if (!isSubmitting && !isLoading) {
                      e.target.style.backgroundColor = '#2563eb';
                    }
                  }}
                >
                  {isSubmitting || isLoading ? (
                    <>
                      <div style={{
                        width: '16px',
                        height: '16px',
                        border: '2px solid #ffffff',
                        borderTop: '2px solid transparent',
                        borderRadius: '50%',
                        animation: 'spin 1s linear infinite',
                        marginLeft: '8px'
                      }}></div>
                      جاري تسجيل الدخول...
                    </>
                  ) : (
                    'تسجيل الدخول'
                  )}
                </button>
              </div>

              {/* Register Link */}
              <div style={{ textAlign: 'center' }}>
                <p style={{ fontSize: '14px', color: '#6b7280' }}>
                  ليس لديك حساب؟{' '}
                  <Link
                    href="/auth/register"
                    style={{
                      fontWeight: '500',
                      color: '#2563eb',
                      textDecoration: 'none'
                    }}
                    onMouseOver={(e) => e.target.style.color = '#1d4ed8'}
                    onMouseOut={(e) => e.target.style.color = '#2563eb'}
                  >
                    إنشاء حساب جديد
                  </Link>
                </p>
              </div>
            </form>
          </div>
        </div>

        {/* Footer */}
        <div style={{ textAlign: 'center', marginTop: '32px' }}>
          <p style={{ fontSize: '12px', color: '#9ca3af' }}>
            نظام إدارة الفواتير © 2024
          </p>
        </div>
      </div>

      {/* Add CSS animation */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}
