const express = require('express');
const cors = require('cors');
const app = express();
const PORT = 8000;

// Middleware
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());

// Mock data
const mockClients = [
  {
    id: "1",
    name: "شركة الأمل للتجارة",
    email: "<EMAIL>",
    phone: "+966501234567",
    address: "شارع الملك فهد، الرياض",
    city: "الرياض",
    country: "المملكة العربية السعودية",
    taxNumber: "*********",
    isActive: true,
    createdAt: "2024-01-01",
    updatedAt: "2024-01-01"
  },
  {
    id: "2",
    name: "مؤسسة النور",
    email: "<EMAIL>",
    phone: "+966507654321",
    address: "طريق الملك عبدالعزيز، جدة",
    city: "جدة",
    country: "المملكة العربية السعودية",
    taxNumber: "*********",
    isActive: true,
    createdAt: "2024-01-02",
    updatedAt: "2024-01-02"
  },
  {
    id: "3",
    name: "شركة الفجر للمقاولات",
    email: "<EMAIL>",
    phone: "+966512345678",
    address: "شارع الأمير سلطان، الدمام",
    city: "الدمام",
    country: "المملكة العربية السعودية",
    taxNumber: "*********",
    isActive: true,
    createdAt: "2024-01-03",
    updatedAt: "2024-01-03"
  }
];

// Auth endpoints
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  if (email === '<EMAIL>' && password === 'admin123') {
    res.json({
      success: true,
      data: {
        user: {
          id: "1",
          firstName: "أحمد",
          lastName: "محمد",
          email: "<EMAIL>",
          phone: "+966501234567",
          role: "admin",
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z"
        },
        tenant: {
          id: "1",
          name: "شركة الأمل للتجارة",
          domain: "alamal"
        },
        token: "mock-jwt-token-" + Date.now()
      },
      message: "تم تسجيل الدخول بنجاح"
    });
  } else {
    res.status(401).json({
      success: false,
      message: "البريد الإلكتروني أو كلمة المرور غير صحيحة"
    });
  }
});

app.get('/api/auth/profile', (req, res) => {
  res.json({
    success: true,
    data: {
      user: {
        id: "1",
        firstName: "أحمد",
        lastName: "محمد",
        email: "<EMAIL>",
        phone: "+966501234567",
        role: "admin",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z"
      },
      tenant: {
        id: "1",
        name: "شركة الأمل للتجارة",
        domain: "alamal"
      }
    },
    message: "تم جلب بيانات المستخدم بنجاح"
  });
});

// Clients endpoints
app.get('/api/clients', (req, res) => {
  const { page = 1, limit = 10, search = '' } = req.query;
  
  let clients = mockClients;
  
  if (search) {
    clients = clients.filter(client => 
      client.name.includes(search) || 
      client.email.includes(search) || 
      client.phone.includes(search)
    );
  }
  
  const total = clients.length;
  const offset = (page - 1) * limit;
  const paginatedClients = clients.slice(offset, offset + parseInt(limit));
  
  res.json({
    success: true,
    data: paginatedClients,
    message: "تم جلب العملاء بنجاح",
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      totalPages: Math.ceil(total / limit)
    }
  });
});

app.post('/api/clients', (req, res) => {
  const newClient = {
    id: (mockClients.length + 1).toString(),
    ...req.body,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  mockClients.push(newClient);
  
  res.json({
    success: true,
    data: newClient,
    message: "تم إضافة العميل بنجاح"
  });
});

app.put('/api/clients/:id', (req, res) => {
  const { id } = req.params;
  const clientIndex = mockClients.findIndex(c => c.id === id);
  
  if (clientIndex === -1) {
    return res.status(404).json({
      success: false,
      message: "العميل غير موجود"
    });
  }
  
  mockClients[clientIndex] = {
    ...mockClients[clientIndex],
    ...req.body,
    updatedAt: new Date().toISOString()
  };
  
  res.json({
    success: true,
    data: mockClients[clientIndex],
    message: "تم تحديث العميل بنجاح"
  });
});

app.delete('/api/clients/:id', (req, res) => {
  const { id } = req.params;
  const clientIndex = mockClients.findIndex(c => c.id === id);
  
  if (clientIndex === -1) {
    return res.status(404).json({
      success: false,
      message: "العميل غير موجود"
    });
  }
  
  mockClients.splice(clientIndex, 1);
  
  res.json({
    success: true,
    message: "تم حذف العميل بنجاح"
  });
});

// Dashboard endpoint
app.get('/api/dashboard/stats', (req, res) => {
  res.json({
    success: true,
    data: {
      totalClients: 25,
      totalInvoices: 150,
      totalRevenue: 125000.00,
      thisMonthRevenue: 18000.00,
      lastMonthRevenue: 15000.00,
      pendingAmount: 35000.00,
      overdueInvoices: 8,
      paidInvoices: 120,
      recentInvoices: [],
      topClients: []
    },
    message: "تم جلب إحصائيات لوحة التحكم بنجاح"
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`📊 API Endpoints:`);
  console.log(`   - GET  /api/clients`);
  console.log(`   - POST /api/clients`);
  console.log(`   - PUT  /api/clients/:id`);
  console.log(`   - DELETE /api/clients/:id`);
  console.log(`   - POST /api/auth/login`);
  console.log(`   - GET  /api/auth/profile`);
  console.log(`   - GET  /api/dashboard/stats`);
});
