# 🚀 دليل البدء السريع - نظام إدارة الفواتير

## ⚡ التشغيل السريع (5 دقائق)

### 1. تشغيل النظام
```bash
# تشغيل النظام كاملاً
start-system.bat
```

### 2. الوصول للنظام
- **واجهة الاختبار**: `frontend/index.html` (ستفتح تلقائياً)
- **API Server**: http://localhost:5000
- **API Info**: http://localhost:5000/api

### 3. تسجيل الدخول
```
البريد الإلكتروني: <EMAIL>
كلمة المرور: admin123
الدور: مدير عام (SUPER_ADMIN)
```

## 🔗 الروابط المهمة

### API Endpoints
- **الصحة**: `GET /health`
- **معلومات API**: `GET /api`
- **تسجيل الدخول**: `POST /api/auth/login`
- **العملاء**: `GET /api/clients`
- **الفواتير**: `GET /api/invoices`
- **لوحة التحكم**: `GET /api/dashboard/stats`
- **اختبار قاعدة البيانات**: `GET /api/test-db`

### قاعدة البيانات
- **phpMyAdmin**: http://localhost/phpmyadmin
- **قاعدة البيانات**: `web_invoice_saas`
- **المستخدم**: `invoice_app`
- **كلمة المرور**: `invoice123`

## 📊 البيانات التجريبية

### المؤسسات
1. **شركة التقنية المتقدمة** (advanced-tech.local)
2. **مؤسسة الابتكار الرقمي** (digital-innovation.local)

### المستخدمون
- **<EMAIL>** / admin123 (مدير عام)
- **<EMAIL>** / admin123 (مستخدم عادي)
- **<EMAIL>** / admin123 (مدير)

### العملاء
- شركة البناء الحديث
- مؤسسة التجارة الإلكترونية
- شركة الخدمات المالية

### الفواتير
- **INV-2024-001** (مرسلة - 11,500 ريال)
- **INV-2024-002** (مدفوعة - 8,250 ريال)
- **DIG-2024-001** (مسودة - 17,250 ريال)

## 🧪 اختبار النظام

### 1. فحص صحة الخادم
```bash
curl http://localhost:5000/health
```

### 2. اختبار تسجيل الدخول
```bash
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'
```

### 3. جلب العملاء
```bash
curl http://localhost:5000/api/clients
```

### 4. جلب الفواتير
```bash
curl http://localhost:5000/api/invoices
```

### 5. إحصائيات لوحة التحكم
```bash
curl http://localhost:5000/api/dashboard/stats
```

## 🌟 الميزات المتاحة

### ✅ مكتملة
- [x] إعداد قاعدة البيانات MySQL
- [x] API Server أساسي
- [x] مصادقة المستخدمين
- [x] إدارة العملاء (عرض)
- [x] إدارة الفواتير (عرض)
- [x] لوحة التحكم والإحصائيات
- [x] واجهة اختبار HTML
- [x] دعم اللغة العربية
- [x] بيانات تجريبية شاملة

### 🚧 قيد التطوير
- [ ] إنشاء وتعديل العملاء
- [ ] إنشاء وتعديل الفواتير
- [ ] إرسال الفواتير بالبريد الإلكتروني
- [ ] تصدير PDF
- [ ] واجهة مستخدم متقدمة (React/Next.js)
- [ ] تكامل مع بوابات الدفع

## 📁 هيكل المشروع

```
web_invoice/
├── backend/
│   ├── server-basic.js      # خادم API بسيط
│   ├── src/                 # كود TypeScript (قيد التطوير)
│   └── package.json
├── frontend/
│   └── index.html           # واجهة اختبار
├── database/
│   └── web_invoice_saas_complete.sql
├── docs/                    # الوثائق
├── scripts/                 # سكريبتات الإعداد
├── start-system.bat         # تشغيل النظام
└── QUICK-START.md          # هذا الملف
```

## 🔧 استكشاف الأخطاء

### مشكلة: الخادم لا يعمل
**الحل:**
1. تأكد من تثبيت Node.js
2. تحقق من أن المنفذ 5000 غير مستخدم
3. شغل الخادم يدوياً: `node backend/server-basic.js`

### مشكلة: قاعدة البيانات غير متاحة
**الحل:**
1. تأكد من تشغيل XAMPP (MySQL)
2. تحقق من استيراد ملف قاعدة البيانات
3. اختبر الاتصال: `node scripts/test-xampp-connection.js`

### مشكلة: واجهة الاختبار لا تعمل
**الحل:**
1. تأكد من تشغيل الخادم أولاً
2. افتح `frontend/index.html` في المتصفح
3. تحقق من console للأخطاء

## 📞 الدعم

### الوثائق
- [دليل إعداد XAMPP](docs/XAMPP-SETUP.md)
- [دليل استيراد قاعدة البيانات](docs/database-import-guide.md)
- [وثائق API](docs/api-documentation.md)

### الملفات المفيدة
- `scripts/setup-complete-xampp.bat` - إعداد شامل
- `scripts/test-xampp-connection.js` - اختبار قاعدة البيانات
- `database/web_invoice_saas_complete.sql` - ملف قاعدة البيانات

## 🎯 الخطوات التالية

1. **جرب النظام**: استخدم واجهة الاختبار لتجربة جميع الميزات
2. **راجع البيانات**: تحقق من phpMyAdmin لرؤية البيانات التجريبية
3. **اقرأ الوثائق**: راجع مجلد `docs` للمزيد من التفاصيل
4. **طور الميزات**: ابدأ في تطوير الميزات المتقدمة

---

**🚀 النظام جاهز للاستخدام! استمتع بتجربة نظام إدارة الفواتير.**
