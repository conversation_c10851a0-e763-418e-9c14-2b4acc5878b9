@echo off
chcp 65001 >nul
title نظام إدارة الفواتير الكامل - Web Invoice SaaS

echo ========================================
echo      🚀 نظام إدارة الفواتير الكامل
echo        Web Invoice SaaS
echo ========================================
echo.

REM الحصول على مسار المشروع
set PROJECT_ROOT=%~dp0
cd /d "%PROJECT_ROOT%"

echo 📁 مسار المشروع: %CD%
echo.

echo ========================================
echo           فحص المتطلبات
echo ========================================
echo.

REM فحص Node.js
node --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js غير مثبت
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo ✅ Node.js مثبت: %NODE_VERSION%
)

REM فحص npm
npm --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ npm غير متاح
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
    echo ✅ npm متاح: %NPM_VERSION%
)

echo.
echo ========================================
echo         إعداد Backend
echo ========================================
echo.

REM التحقق من وجود ملف .env في Backend
if not exist "backend\.env" (
    echo ⚠️  إنشاء ملف .env للـ Backend...
    copy "backend\.env.example" "backend\.env" >nul 2>&1
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل في إنشاء ملف .env
        echo يرجى نسخ .env.example إلى .env يدوياً في مجلد backend
        pause
        exit /b 1
    )
    echo ✅ تم إنشاء ملف .env للـ Backend
) else (
    echo ✅ ملف .env موجود للـ Backend
)

REM التحقق من تثبيت تبعيات Backend
if not exist "backend\node_modules" (
    echo ⚠️  تثبيت تبعيات Backend...
    cd backend
    call npm install --no-optional
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل في تثبيت تبعيات Backend
        echo جاري المحاولة مع الخادم البسيط...
        cd ..
        goto :simple_backend
    )
    cd ..
    echo ✅ تم تثبيت تبعيات Backend
) else (
    echo ✅ تبعيات Backend مثبتة
)

echo.
echo ========================================
echo         إعداد Frontend
echo ========================================
echo.

REM التحقق من تثبيت تبعيات Frontend
if not exist "frontend\node_modules" (
    echo ⚠️  تثبيت تبعيات Frontend...
    cd frontend
    call npm install --no-optional
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل في تثبيت تبعيات Frontend
        echo سيتم تشغيل Backend فقط
        cd ..
        set FRONTEND_AVAILABLE=false
        goto :start_backend
    )
    cd ..
    echo ✅ تم تثبيت تبعيات Frontend
    set FRONTEND_AVAILABLE=true
) else (
    echo ✅ تبعيات Frontend مثبتة
    set FRONTEND_AVAILABLE=true
)

goto :start_full_system

:simple_backend
echo.
echo ========================================
echo      تشغيل Backend البسيط
echo ========================================
echo.

echo 🚀 تشغيل Backend API البسيط...
cd backend
start cmd /k "title Backend API Server (Simple) - http://localhost:5000 && echo 🚀 Backend API Server (Simple) && echo المنفذ: 5000 && echo الحالة: يعمل && echo. && echo للإيقاف: اضغط Ctrl+C && echo. && node server-basic.js"
cd ..
set FRONTEND_AVAILABLE=false
goto :show_info

:start_backend
echo.
echo ========================================
echo         تشغيل Backend
echo ========================================
echo.

echo 🚀 تشغيل Backend API Server...
cd backend
start cmd /k "title Backend API Server - http://localhost:5000 && echo 🚀 Backend API Server && echo المنفذ: 5000 && echo الحالة: يعمل && echo. && echo للإيقاف: اضغط Ctrl+C && echo. && npm run dev"
cd ..
goto :show_info

:start_full_system
echo.
echo ========================================
echo         تشغيل النظام الكامل
echo ========================================
echo.

echo 🚀 تشغيل Backend API Server...
cd backend
start cmd /k "title Backend API Server - http://localhost:5000 && echo 🚀 Backend API Server && echo المنفذ: 5000 && echo الحالة: يعمل && echo. && echo للإيقاف: اضغط Ctrl+C && echo. && npm run dev"
cd ..

REM انتظار قليل لبدء Backend
echo ⏳ انتظار بدء Backend...
timeout /t 5 >nul

if "%FRONTEND_AVAILABLE%"=="true" (
    echo 🌐 تشغيل Frontend Web Application...
    cd frontend
    start cmd /k "title Frontend Web App - http://localhost:3000 && echo 🌐 Frontend Web Application && echo المنفذ: 3000 && echo الحالة: يعمل && echo. && echo للإيقاف: اضغط Ctrl+C && echo. && npm run dev"
    cd ..
)

:show_info
echo.
echo ========================================
echo            النظام يعمل الآن!
echo ========================================
echo.

echo 🌐 الروابط المتاحة:
echo.
echo    📡 Backend API:
echo       الصحة: http://localhost:5000/health
echo       المعلومات: http://localhost:5000/api
echo       المصادقة: http://localhost:5000/api/auth
echo       العملاء: http://localhost:5000/api/clients
echo       الفواتير: http://localhost:5000/api/invoices
echo       لوحة التحكم: http://localhost:5000/api/dashboard
echo.

if "%FRONTEND_AVAILABLE%"=="true" (
    echo    🌐 Frontend Web Application:
    echo       الرئيسية: http://localhost:3000
    echo       تسجيل الدخول: http://localhost:3000/auth/login
    echo       لوحة التحكم: http://localhost:3000/dashboard
    echo       العملاء: http://localhost:3000/dashboard/clients
    echo       الفواتير: http://localhost:3000/dashboard/invoices
    echo       التقارير: http://localhost:3000/dashboard/reports
    echo       الإعدادات: http://localhost:3000/dashboard/settings
    echo.
) else (
    echo    🌐 واجهة الاختبار البسيطة:
    echo       ملف محلي: frontend\index.html
    echo.
)

echo    🗄️  إدارة قاعدة البيانات:
echo       phpMyAdmin: http://localhost/phpmyadmin
echo       قاعدة البيانات: web_invoice_saas
echo       المستخدم: invoice_app
echo       كلمة المرور: invoice123
echo.

echo 👤 الحسابات التجريبية:
echo.
echo    🔑 مدير عام:
echo       البريد الإلكتروني: <EMAIL>
echo       كلمة المرور: admin123
echo       الدور: SUPER_ADMIN
echo       المؤسسة: شركة التقنية المتقدمة
echo.
echo    👨‍💼 مستخدم عادي:
echo       البريد الإلكتروني: <EMAIL>
echo       كلمة المرور: admin123
echo       الدور: USER
echo       المؤسسة: شركة التقنية المتقدمة
echo.
echo    👩‍💼 مدير:
echo       البريد الإلكتروني: <EMAIL>
echo       كلمة المرور: admin123
echo       الدور: ADMIN
echo       المؤسسة: مؤسسة الابتكار الرقمي
echo.

echo 📊 البيانات التجريبية:
echo.
echo    👥 العملاء:
echo       - شركة البناء الحديث
echo       - مؤسسة التجارة الإلكترونية
echo       - شركة الخدمات المالية
echo.
echo    📄 الفواتير:
echo       - INV-2024-001 (مرسلة - 11,500 ريال)
echo       - INV-2024-002 (مدفوعة - 8,250 ريال)
echo       - DIG-2024-001 (مسودة - 17,250 ريال)
echo.

echo 🔧 أوامر مفيدة:
echo.
echo    اختبار Backend API:
echo       curl http://localhost:5000/health
echo.
echo    اختبار تسجيل الدخول:
echo       curl -X POST http://localhost:5000/api/auth/login ^
echo            -H "Content-Type: application/json" ^
echo            -d "{\"email\":\"<EMAIL>\",\"password\":\"admin123\"}"
echo.
echo    فحص قاعدة البيانات:
echo       node scripts\test-xampp-connection.js
echo.

echo 📋 الميزات المتاحة:
echo.
echo    ✅ مكتملة:
echo       - نظام المصادقة والتفويض
echo       - إدارة العملاء (عرض، بحث، فلترة)
echo       - إدارة الفواتير (عرض، بحث، فلترة)
echo       - لوحة التحكم مع الإحصائيات
echo       - التقارير والمخططات البيانية
echo       - الإعدادات الأساسية
echo       - واجهة مستخدم عربية متجاوبة
echo       - دعم Multi-Tenant
echo       - قاعدة بيانات MySQL
echo.
echo    🚧 قيد التطوير:
echo       - إنشاء وتعديل العملاء
echo       - إنشاء وتعديل الفواتير
echo       - إرسال الفواتير بالبريد الإلكتروني
echo       - تصدير PDF
echo       - تكامل مع بوابات الدفع
echo       - نظام الإشعارات
echo.

REM فتح المتصفح (اختياري)
set /p OPEN_BROWSER="هل تريد فتح النظام في المتصفح؟ (y/n): "
if /i "%OPEN_BROWSER%"=="y" (
    echo 🌐 فتح المتصفح...
    
    if "%FRONTEND_AVAILABLE%"=="true" (
        REM فتح Frontend
        start http://localhost:3000
        timeout /t 2 >nul
    ) else (
        REM فتح واجهة الاختبار البسيطة
        start "" "%PROJECT_ROOT%frontend\index.html"
        timeout /t 2 >nul
    )
    
    REM فتح Backend API info
    start http://localhost:5000/api
    
    REM فتح phpMyAdmin (إذا كان XAMPP يعمل)
    timeout /t 1 >nul
    start http://localhost/phpmyadmin
)

echo.
echo ✨ النظام يعمل بنجاح!
echo.
echo 💡 نصائح:
if "%FRONTEND_AVAILABLE%"=="true" (
    echo    - استخدم http://localhost:3000 للوصول للواجهة الكاملة
) else (
    echo    - استخدم frontend\index.html للاختبار البسيط
)
echo    - استخدم http://localhost:5000/api لاختبار API
echo    - تحقق من phpMyAdmin لرؤية البيانات
echo    - راجع الوثائق في مجلد docs للمزيد من المعلومات
echo.
echo 🛑 لإيقاف النظام:
echo    - أغلق نوافذ الخوادم أو اضغط Ctrl+C في كل نافذة
echo.

echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
