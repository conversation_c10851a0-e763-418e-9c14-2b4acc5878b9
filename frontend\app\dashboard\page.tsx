'use client';

import { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  UsersIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  CalendarIcon,
} from '@heroicons/react/24/outline';
import { dashboardApi } from '@/lib/api';
import { useAuth } from '@/store/auth';
import type { DashboardStats } from '@/types';

// Stats Card Component
interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ComponentType<any>;
  color: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'indigo';
  change?: {
    value: number;
    type: 'increase' | 'decrease';
  };
}

const StatsCard = ({ title, value, icon: Icon, color, change }: StatsCardProps) => {
  const colorClasses = {
    blue: 'bg-blue-500 text-blue-600 bg-blue-50',
    green: 'bg-green-500 text-green-600 bg-green-50',
    yellow: 'bg-yellow-500 text-yellow-600 bg-yellow-50',
    red: 'bg-red-500 text-red-600 bg-red-50',
    purple: 'bg-purple-500 text-purple-600 bg-purple-50',
    indigo: 'bg-indigo-500 text-indigo-600 bg-indigo-50',
  };

  const [bgColor, textColor, lightBg] = colorClasses[color].split(' ');

  return (
    <div className="card">
      <div className="card-body">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className={`w-12 h-12 ${lightBg} rounded-lg flex items-center justify-center`}>
              <Icon className={`w-6 h-6 ${textColor}`} />
            </div>
          </div>
          <div className="mr-4 flex-1">
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            {change && (
              <p className={`text-sm ${
                change.type === 'increase' ? 'text-green-600' : 'text-red-600'
              }`}>
                {change.type === 'increase' ? '+' : '-'}{Math.abs(change.value)}%
                <span className="text-gray-500 mr-1">من الشهر الماضي</span>
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Recent Invoices Component
const RecentInvoices = ({ invoices }: { invoices: any[] }) => {
  const getStatusBadge = (status: string) => {
    const statusMap = {
      DRAFT: { label: 'مسودة', class: 'badge-gray' },
      SENT: { label: 'مرسلة', class: 'badge-blue' },
      PAID: { label: 'مدفوعة', class: 'badge-green' },
      OVERDUE: { label: 'متأخرة', class: 'badge-red' },
      CANCELLED: { label: 'ملغية', class: 'badge-gray' },
    };
    
    const statusInfo = statusMap[status as keyof typeof statusMap] || statusMap.DRAFT;
    return (
      <span className={`badge ${statusInfo.class}`}>
        {statusInfo.label}
      </span>
    );
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold text-gray-900">أحدث الفواتير</h3>
      </div>
      <div className="card-body p-0">
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="table-header-cell">رقم الفاتورة</th>
                <th className="table-header-cell">العميل</th>
                <th className="table-header-cell">المبلغ</th>
                <th className="table-header-cell">الحالة</th>
                <th className="table-header-cell">تاريخ الإصدار</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {invoices.map((invoice) => (
                <tr key={invoice.id} className="table-row">
                  <td className="table-cell font-medium">
                    {invoice.invoiceNumber}
                  </td>
                  <td className="table-cell">
                    {invoice.client?.name || 'غير محدد'}
                  </td>
                  <td className="table-cell">
                    {invoice.totalAmount?.toLocaleString('ar-SA')} {invoice.currency}
                  </td>
                  <td className="table-cell">
                    {getStatusBadge(invoice.status)}
                  </td>
                  <td className="table-cell text-gray-500">
                    {new Date(invoice.issueDate).toLocaleDateString('ar-SA')}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

// Top Clients Component
const TopClients = ({ clients }: { clients: any[] }) => {
  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold text-gray-900">أفضل العملاء</h3>
      </div>
      <div className="card-body">
        <div className="space-y-4">
          {clients.map((client, index) => (
            <div key={client.id} className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center me-3">
                  <span className="text-sm font-medium text-blue-600">
                    {index + 1}
                  </span>
                </div>
                <div>
                  <p className="font-medium text-gray-900">{client.name}</p>
                  <p className="text-sm text-gray-500">
                    {client.totalInvoices} فاتورة
                  </p>
                </div>
              </div>
              <div className="text-end">
                <p className="font-medium text-gray-900">
                  {client.totalAmount?.toLocaleString('ar-SA')} ريال
                </p>
                <p className="text-sm text-gray-500">
                  مدفوع: {client.paidAmount?.toLocaleString('ar-SA')}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default function DashboardPage() {
  const { user, tenant } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Fetch dashboard stats
  const {
    data: statsResponse,
    isLoading: statsLoading,
    error: statsError,
  } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: dashboardApi.getStats,
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });

  const stats = statsResponse?.data;

  if (statsLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="loading-spinner w-8 h-8 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل لوحة التحكم...</p>
        </div>
      </div>
    );
  }

  if (statsError) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            خطأ في تحميل البيانات
          </h2>
          <p className="text-gray-600">
            حدث خطأ أثناء تحميل بيانات لوحة التحكم
          </p>
        </div>
      </div>
    );
  }

  const revenueChange = stats?.lastMonthRevenue 
    ? ((stats.thisMonthRevenue - stats.lastMonthRevenue) / stats.lastMonthRevenue) * 100
    : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            مرحباً، {user?.firstName} {user?.lastName}
          </h1>
          <p className="text-gray-600">
            {tenant?.name} - {currentTime.toLocaleDateString('ar-SA', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })}
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <div className="flex items-center text-sm text-gray-500">
            <CalendarIcon className="w-4 h-4 me-1" />
            آخر تحديث: {currentTime.toLocaleTimeString('ar-SA', {
              hour: '2-digit',
              minute: '2-digit',
            })}
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="إجمالي العملاء"
          value={stats?.totalClients || 0}
          icon={UsersIcon}
          color="blue"
        />
        <StatsCard
          title="إجمالي الفواتير"
          value={stats?.totalInvoices || 0}
          icon={DocumentTextIcon}
          color="indigo"
        />
        <StatsCard
          title="إجمالي الإيرادات"
          value={`${(stats?.totalRevenue || 0).toLocaleString('ar-SA')} ريال`}
          icon={CurrencyDollarIcon}
          color="green"
          change={revenueChange ? {
            value: Math.round(revenueChange),
            type: revenueChange > 0 ? 'increase' : 'decrease',
          } : undefined}
        />
        <StatsCard
          title="فواتير متأخرة"
          value={stats?.overdueInvoices || 0}
          icon={ExclamationTriangleIcon}
          color="red"
        />
      </div>

      {/* Secondary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatsCard
          title="فواتير مدفوعة"
          value={stats?.paidInvoices || 0}
          icon={ChartBarIcon}
          color="green"
        />
        <StatsCard
          title="مبلغ معلق"
          value={`${(stats?.pendingAmount || 0).toLocaleString('ar-SA')} ريال`}
          icon={CurrencyDollarIcon}
          color="yellow"
        />
        <StatsCard
          title="إيرادات هذا الشهر"
          value={`${(stats?.thisMonthRevenue || 0).toLocaleString('ar-SA')} ريال`}
          icon={ChartBarIcon}
          color="purple"
        />
      </div>

      {/* Recent Data */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RecentInvoices invoices={stats?.recentInvoices || []} />
        <TopClients clients={stats?.topClients || []} />
      </div>
    </div>
  );
}
