'use client';

import { useAuth } from '../store/auth-store';

export default function DashboardPage() {
  const { user, tenant } = useAuth();

  return (
    <div style={{
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: 'rtl',
      padding: '24px'
    }}>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
        {/* Header */}
        <div>
          <h1 style={{
            fontSize: '28px',
            fontWeight: 'bold',
            color: '#111827',
            margin: '0 0 8px 0'
          }}>
            مرحباً، {user?.firstName} {user?.lastName}
          </h1>
          <p style={{
            color: '#6b7280',
            margin: 0
          }}>
            {tenant?.name} - لوحة التحكم
          </p>
        </div>

        {/* Stats Cards */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '24px'
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
            padding: '24px'
          }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{
                width: '48px',
                height: '48px',
                backgroundColor: '#dbeafe',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginLeft: '16px'
              }}>
                <span style={{ fontSize: '24px' }}>👥</span>
              </div>
              <div>
                <p style={{
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#6b7280',
                  margin: '0 0 4px 0'
                }}>
                  إجمالي العملاء
                </p>
                <p style={{
                  fontSize: '24px',
                  fontWeight: 'bold',
                  color: '#111827',
                  margin: 0
                }}>
                  25
                </p>
              </div>
            </div>
          </div>

          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
            padding: '24px'
          }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{
                width: '48px',
                height: '48px',
                backgroundColor: '#e0e7ff',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginLeft: '16px'
              }}>
                <span style={{ fontSize: '24px' }}>📄</span>
              </div>
              <div>
                <p style={{
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#6b7280',
                  margin: '0 0 4px 0'
                }}>
                  إجمالي الفواتير
                </p>
                <p style={{
                  fontSize: '24px',
                  fontWeight: 'bold',
                  color: '#111827',
                  margin: 0
                }}>
                  150
                </p>
              </div>
            </div>
          </div>

          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
            padding: '24px'
          }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{
                width: '48px',
                height: '48px',
                backgroundColor: '#dcfce7',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginLeft: '16px'
              }}>
                <span style={{ fontSize: '24px' }}>💰</span>
              </div>
              <div>
                <p style={{
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#6b7280',
                  margin: '0 0 4px 0'
                }}>
                  إجمالي الإيرادات
                </p>
                <p style={{
                  fontSize: '24px',
                  fontWeight: 'bold',
                  color: '#111827',
                  margin: 0
                }}>
                  125,000 ريال
                </p>
              </div>
            </div>
          </div>

          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
            padding: '24px'
          }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{
                width: '48px',
                height: '48px',
                backgroundColor: '#fee2e2',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginLeft: '16px'
              }}>
                <span style={{ fontSize: '24px' }}>⚠️</span>
              </div>
              <div>
                <p style={{
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#6b7280',
                  margin: '0 0 4px 0'
                }}>
                  فواتير متأخرة
                </p>
                <p style={{
                  fontSize: '24px',
                  fontWeight: 'bold',
                  color: '#111827',
                  margin: 0
                }}>
                  8
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
          overflow: 'hidden'
        }}>
          <div style={{
            padding: '20px 24px',
            borderBottom: '1px solid #e5e7eb'
          }}>
            <h3 style={{
              fontSize: '18px',
              fontWeight: '600',
              color: '#111827',
              margin: 0
            }}>
              النشاط الأخير
            </h3>
          </div>
          <div style={{ padding: '24px' }}>
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '16px'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                padding: '12px',
                backgroundColor: '#f9fafb',
                borderRadius: '8px'
              }}>
                <div style={{
                  width: '32px',
                  height: '32px',
                  backgroundColor: '#dbeafe',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginLeft: '12px'
                }}>
                  <span style={{ fontSize: '16px' }}>📄</span>
                </div>
                <div style={{ flex: 1 }}>
                  <p style={{
                    fontWeight: '500',
                    color: '#111827',
                    margin: '0 0 2px 0'
                  }}>
                    تم إنشاء فاتورة جديدة
                  </p>
                  <p style={{
                    fontSize: '14px',
                    color: '#6b7280',
                    margin: 0
                  }}>
                    فاتورة رقم INV-2024-001 لشركة الأمل للتجارة
                  </p>
                </div>
                <span style={{
                  fontSize: '14px',
                  color: '#6b7280'
                }}>
                  منذ ساعتين
                </span>
              </div>

              <div style={{
                display: 'flex',
                alignItems: 'center',
                padding: '12px',
                backgroundColor: '#f9fafb',
                borderRadius: '8px'
              }}>
                <div style={{
                  width: '32px',
                  height: '32px',
                  backgroundColor: '#dcfce7',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginLeft: '12px'
                }}>
                  <span style={{ fontSize: '16px' }}>💰</span>
                </div>
                <div style={{ flex: 1 }}>
                  <p style={{
                    fontWeight: '500',
                    color: '#111827',
                    margin: '0 0 2px 0'
                  }}>
                    تم استلام دفعة
                  </p>
                  <p style={{
                    fontSize: '14px',
                    color: '#6b7280',
                    margin: 0
                  }}>
                    دفعة بقيمة 9,200 ريال من مؤسسة النور
                  </p>
                </div>
                <span style={{
                  fontSize: '14px',
                  color: '#6b7280'
                }}>
                  منذ 4 ساعات
                </span>
              </div>

              <div style={{
                display: 'flex',
                alignItems: 'center',
                padding: '12px',
                backgroundColor: '#f9fafb',
                borderRadius: '8px'
              }}>
                <div style={{
                  width: '32px',
                  height: '32px',
                  backgroundColor: '#e0e7ff',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginLeft: '12px'
                }}>
                  <span style={{ fontSize: '16px' }}>👤</span>
                </div>
                <div style={{ flex: 1 }}>
                  <p style={{
                    fontWeight: '500',
                    color: '#111827',
                    margin: '0 0 2px 0'
                  }}>
                    تم إضافة عميل جديد
                  </p>
                  <p style={{
                    fontSize: '14px',
                    color: '#6b7280',
                    margin: 0
                  }}>
                    شركة الفجر للمقاولات
                  </p>
                </div>
                <span style={{
                  fontSize: '14px',
                  color: '#6b7280'
                }}>
                  أمس
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
