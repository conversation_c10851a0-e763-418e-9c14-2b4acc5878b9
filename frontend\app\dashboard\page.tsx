'use client';

import { useEffect, useState } from 'react';
import { apiClient, type DashboardStats } from '../lib/api-client';
import { useAuth } from '../store/auth-store';

// Stats Card Component
interface StatsCardProps {
  title: string;
  value: string | number;
  icon: string;
  color: string;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
  };
}

const StatsCard = ({ title, value, icon, color, change }: StatsCardProps) => {
  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
      overflow: 'hidden'
    }}>
      <div style={{ padding: '24px' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ flexShrink: 0 }}>
            <div style={{
              width: '48px',
              height: '48px',
              backgroundColor: color,
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <span style={{ fontSize: '24px' }}>{icon}</span>
            </div>
          </div>
          <div style={{ marginRight: '16px', flex: 1 }}>
            <p style={{
              fontSize: '14px',
              fontWeight: '500',
              color: '#6b7280',
              margin: '0 0 4px 0'
            }}>
              {title}
            </p>
            <p style={{
              fontSize: '24px',
              fontWeight: 'bold',
              color: '#111827',
              margin: '0 0 4px 0'
            }}>
              {value}
            </p>
            {change && (
              <p style={{
                fontSize: '14px',
                color: change.type === 'increase' ? '#10b981' : '#ef4444',
                margin: 0
              }}>
                {change.type === 'increase' ? '+' : '-'}{Math.abs(change.value)}%
                <span style={{ color: '#6b7280', marginRight: '4px' }}>من الشهر الماضي</span>
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Recent Invoices Component
const RecentInvoices = ({ invoices }: { invoices: any[] }) => {
  const getStatusBadge = (status: string) => {
    const statusMap = {
      DRAFT: { label: 'مسودة', color: '#6b7280' },
      SENT: { label: 'مرسلة', color: '#3b82f6' },
      PAID: { label: 'مدفوعة', color: '#10b981' },
      OVERDUE: { label: 'متأخرة', color: '#ef4444' },
      CANCELLED: { label: 'ملغية', color: '#6b7280' },
    };
    
    const statusInfo = statusMap[status as keyof typeof statusMap] || statusMap.DRAFT;
    return (
      <span style={{
        display: 'inline-flex',
        alignItems: 'center',
        padding: '2px 8px',
        borderRadius: '12px',
        fontSize: '12px',
        fontWeight: '500',
        backgroundColor: `${statusInfo.color}20`,
        color: statusInfo.color
      }}>
        {statusInfo.label}
      </span>
    );
  };

  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
      overflow: 'hidden'
    }}>
      <div style={{
        padding: '20px 24px',
        borderBottom: '1px solid #e5e7eb'
      }}>
        <h3 style={{
          fontSize: '18px',
          fontWeight: '600',
          color: '#111827',
          margin: 0
        }}>
          أحدث الفواتير
        </h3>
      </div>
      <div style={{ padding: 0 }}>
        <div style={{ overflowX: 'auto' }}>
          <table style={{
            width: '100%',
            borderCollapse: 'collapse'
          }}>
            <thead style={{ backgroundColor: '#f9fafb' }}>
              <tr>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  رقم الفاتورة
                </th>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  العميل
                </th>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  المبلغ
                </th>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  الحالة
                </th>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  تاريخ الإصدار
                </th>
              </tr>
            </thead>
            <tbody style={{ backgroundColor: 'white' }}>
              {invoices.map((invoice) => (
                <tr key={invoice.id} style={{
                  borderBottom: '1px solid #e5e7eb'
                }}>
                  <td style={{
                    padding: '16px 24px',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: '#111827'
                  }}>
                    {invoice.invoiceNumber}
                  </td>
                  <td style={{
                    padding: '16px 24px',
                    fontSize: '14px',
                    color: '#111827'
                  }}>
                    {invoice.client?.name || 'غير محدد'}
                  </td>
                  <td style={{
                    padding: '16px 24px',
                    fontSize: '14px',
                    color: '#111827'
                  }}>
                    {invoice.totalAmount?.toLocaleString('ar-SA')} {invoice.currency}
                  </td>
                  <td style={{
                    padding: '16px 24px',
                    fontSize: '14px'
                  }}>
                    {getStatusBadge(invoice.status)}
                  </td>
                  <td style={{
                    padding: '16px 24px',
                    fontSize: '14px',
                    color: '#6b7280'
                  }}>
                    {new Date(invoice.issueDate).toLocaleDateString('ar-SA')}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

// Top Clients Component
const TopClients = ({ clients }: { clients: any[] }) => {
  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
      overflow: 'hidden'
    }}>
      <div style={{
        padding: '20px 24px',
        borderBottom: '1px solid #e5e7eb'
      }}>
        <h3 style={{
          fontSize: '18px',
          fontWeight: '600',
          color: '#111827',
          margin: 0
        }}>
          أفضل العملاء
        </h3>
      </div>
      <div style={{ padding: '24px' }}>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          {clients.map((client, index) => (
            <div key={client.id} style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '32px',
                  height: '32px',
                  backgroundColor: '#dbeafe',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginLeft: '12px'
                }}>
                  <span style={{
                    fontSize: '14px',
                    fontWeight: '500',
                    color: '#2563eb'
                  }}>
                    {index + 1}
                  </span>
                </div>
                <div>
                  <p style={{
                    fontWeight: '500',
                    color: '#111827',
                    margin: '0 0 2px 0'
                  }}>
                    {client.name}
                  </p>
                  <p style={{
                    fontSize: '14px',
                    color: '#6b7280',
                    margin: 0
                  }}>
                    {client.totalInvoices} فاتورة
                  </p>
                </div>
              </div>
              <div style={{ textAlign: 'left' }}>
                <p style={{
                  fontWeight: '500',
                  color: '#111827',
                  margin: '0 0 2px 0'
                }}>
                  {client.totalAmount?.toLocaleString('ar-SA')} ريال
                </p>
                <p style={{
                  fontSize: '14px',
                  color: '#6b7280',
                  margin: 0
                }}>
                  مدفوع: {client.paidAmount?.toLocaleString('ar-SA')}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default function DashboardPage() {
  const { user, tenant } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Fetch dashboard stats
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await apiClient.getDashboardStats();
        if (response.success) {
          setStats(response.data || null);
        } else {
          setError(response.message);
        }
      } catch (err: any) {
        setError(err.message || 'حدث خطأ في تحميل البيانات');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return (
      <div style={{
        minHeight: '400px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '32px',
            height: '32px',
            border: '2px solid #e5e7eb',
            borderTop: '2px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <p style={{ color: '#6b7280' }}>جاري تحميل لوحة التحكم...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        minHeight: '400px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>⚠️</div>
          <h2 style={{
            fontSize: '20px',
            fontWeight: '600',
            color: '#111827',
            marginBottom: '8px'
          }}>
            خطأ في تحميل البيانات
          </h2>
          <p style={{ color: '#6b7280' }}>
            {error}
          </p>
        </div>
      </div>
    );
  }

  const revenueChange = stats?.lastMonthRevenue 
    ? ((stats.thisMonthRevenue - stats.lastMonthRevenue) / stats.lastMonthRevenue) * 100
    : 0;

  return (
    <div style={{
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: 'rtl'
    }}>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '8px'
        }} className="sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 style={{
              fontSize: '28px',
              fontWeight: 'bold',
              color: '#111827',
              margin: '0 0 4px 0'
            }}>
              مرحباً، {user?.firstName} {user?.lastName}
            </h1>
            <p style={{
              color: '#6b7280',
              margin: 0
            }}>
              {tenant?.name} - {currentTime.toLocaleDateString('ar-SA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <div style={{
              display: 'flex',
              alignItems: 'center',
              fontSize: '14px',
              color: '#6b7280'
            }}>
              <span style={{ marginLeft: '4px' }}>📅</span>
              آخر تحديث: {currentTime.toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
              })}
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '24px'
        }}>
          <StatsCard
            title="إجمالي العملاء"
            value={stats?.totalClients || 0}
            icon="👥"
            color="#dbeafe"
          />
          <StatsCard
            title="إجمالي الفواتير"
            value={stats?.totalInvoices || 0}
            icon="📄"
            color="#e0e7ff"
          />
          <StatsCard
            title="إجمالي الإيرادات"
            value={`${(stats?.totalRevenue || 0).toLocaleString('ar-SA')} ريال`}
            icon="💰"
            color="#dcfce7"
            change={revenueChange ? {
              value: Math.round(revenueChange),
              type: revenueChange > 0 ? 'increase' : 'decrease',
            } : undefined}
          />
          <StatsCard
            title="فواتير متأخرة"
            value={stats?.overdueInvoices || 0}
            icon="⚠️"
            color="#fee2e2"
          />
        </div>

        {/* Secondary Stats */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '24px'
        }}>
          <StatsCard
            title="فواتير مدفوعة"
            value={stats?.paidInvoices || 0}
            icon="✅"
            color="#dcfce7"
          />
          <StatsCard
            title="مبلغ معلق"
            value={`${(stats?.pendingAmount || 0).toLocaleString('ar-SA')} ريال`}
            icon="⏳"
            color="#fef3c7"
          />
          <StatsCard
            title="إيرادات هذا الشهر"
            value={`${(stats?.thisMonthRevenue || 0).toLocaleString('ar-SA')} ريال`}
            icon="📈"
            color="#f3e8ff"
          />
        </div>

        {/* Recent Data */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr',
          gap: '24px'
        }} className="lg:grid-cols-2">
          <RecentInvoices invoices={stats?.recentInvoices || []} />
          <TopClients clients={stats?.topClients || []} />
        </div>
      </div>

      {/* Add CSS animation */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}
