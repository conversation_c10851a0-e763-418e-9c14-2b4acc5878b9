import { Request, Response } from 'express';
import { InvoiceService } from '@/services/invoiceService';
import { CreateInvoiceRequest, UpdateInvoiceRequest, InvoiceFilters } from '@/types/models';

export class InvoiceController {
  // إنشاء فاتورة جديدة
  static async createInvoice(req: Request, res: Response) {
    try {
      if (!req.user || !req.tenant) {
        return res.status(401).json({
          success: false,
          message: 'المصادقة مطلوبة',
          errors: ['AUTHENTICATION_REQUIRED'],
        });
      }

      const data: CreateInvoiceRequest = req.body;

      // التحقق من البيانات المطلوبة
      if (!data.clientId) {
        return res.status(400).json({
          success: false,
          message: 'معرف العميل مطلوب',
          errors: ['CLIENT_ID_REQUIRED'],
        });
      }

      if (!data.dueDate) {
        return res.status(400).json({
          success: false,
          message: 'تاريخ الاستحقاق مطلوب',
          errors: ['DUE_DATE_REQUIRED'],
        });
      }

      if (!data.items || data.items.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'عناصر الفاتورة مطلوبة',
          errors: ['INVOICE_ITEMS_REQUIRED'],
        });
      }

      // التحقق من صحة عناصر الفاتورة
      for (const item of data.items) {
        if (!item.description || item.quantity <= 0 || item.unitPrice < 0) {
          return res.status(400).json({
            success: false,
            message: 'بيانات عناصر الفاتورة غير صحيحة',
            errors: ['INVALID_INVOICE_ITEMS'],
          });
        }
      }

      const result = await InvoiceService.createInvoice(req.user.tenantId, req.user.userId, data);

      if (!result.success) {
        return res.status(400).json(result);
      }

      return res.status(201).json(result);
    } catch (error) {
      console.error('Create invoice controller error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ داخلي في الخادم',
        errors: ['INTERNAL_ERROR'],
      });
    }
  }

  // الحصول على جميع الفواتير
  static async getInvoices(req: Request, res: Response) {
    try {
      if (!req.user || !req.tenant) {
        return res.status(401).json({
          success: false,
          message: 'المصادقة مطلوبة',
          errors: ['AUTHENTICATION_REQUIRED'],
        });
      }

      const filters: InvoiceFilters = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 10,
        status: req.query.status as any,
        clientId: req.query.clientId as string,
        startDate: req.query.startDate as string,
        endDate: req.query.endDate as string,
        search: req.query.search as string,
      };

      const result = await InvoiceService.getInvoices(req.user.tenantId, filters);

      if (!result.success) {
        return res.status(400).json(result);
      }

      return res.status(200).json(result);
    } catch (error) {
      console.error('Get invoices controller error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ داخلي في الخادم',
        errors: ['INTERNAL_ERROR'],
      });
    }
  }

  // الحصول على فاتورة واحدة
  static async getInvoiceById(req: Request, res: Response) {
    try {
      if (!req.user || !req.tenant) {
        return res.status(401).json({
          success: false,
          message: 'المصادقة مطلوبة',
          errors: ['AUTHENTICATION_REQUIRED'],
        });
      }

      const { invoiceId } = req.params;

      if (!invoiceId) {
        return res.status(400).json({
          success: false,
          message: 'معرف الفاتورة مطلوب',
          errors: ['INVOICE_ID_REQUIRED'],
        });
      }

      const result = await InvoiceService.getInvoiceById(req.user.tenantId, invoiceId);

      if (!result.success) {
        return res.status(404).json(result);
      }

      return res.status(200).json(result);
    } catch (error) {
      console.error('Get invoice controller error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ داخلي في الخادم',
        errors: ['INTERNAL_ERROR'],
      });
    }
  }

  // تحديث فاتورة
  static async updateInvoice(req: Request, res: Response) {
    try {
      if (!req.user || !req.tenant) {
        return res.status(401).json({
          success: false,
          message: 'المصادقة مطلوبة',
          errors: ['AUTHENTICATION_REQUIRED'],
        });
      }

      const { invoiceId } = req.params;
      const data: UpdateInvoiceRequest = req.body;

      if (!invoiceId) {
        return res.status(400).json({
          success: false,
          message: 'معرف الفاتورة مطلوب',
          errors: ['INVOICE_ID_REQUIRED'],
        });
      }

      // التحقق من صحة عناصر الفاتورة إذا تم تقديمها
      if (data.items) {
        for (const item of data.items) {
          if (!item.description || item.quantity <= 0 || item.unitPrice < 0) {
            return res.status(400).json({
              success: false,
              message: 'بيانات عناصر الفاتورة غير صحيحة',
              errors: ['INVALID_INVOICE_ITEMS'],
            });
          }
        }
      }

      const result = await InvoiceService.updateInvoice(req.user.tenantId, invoiceId, data);

      if (!result.success) {
        return res.status(400).json(result);
      }

      return res.status(200).json(result);
    } catch (error) {
      console.error('Update invoice controller error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ داخلي في الخادم',
        errors: ['INTERNAL_ERROR'],
      });
    }
  }

  // حذف فاتورة
  static async deleteInvoice(req: Request, res: Response) {
    try {
      if (!req.user || !req.tenant) {
        return res.status(401).json({
          success: false,
          message: 'المصادقة مطلوبة',
          errors: ['AUTHENTICATION_REQUIRED'],
        });
      }

      const { invoiceId } = req.params;

      if (!invoiceId) {
        return res.status(400).json({
          success: false,
          message: 'معرف الفاتورة مطلوب',
          errors: ['INVOICE_ID_REQUIRED'],
        });
      }

      const result = await InvoiceService.deleteInvoice(req.user.tenantId, invoiceId);

      if (!result.success) {
        return res.status(400).json(result);
      }

      return res.status(200).json(result);
    } catch (error) {
      console.error('Delete invoice controller error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ داخلي في الخادم',
        errors: ['INTERNAL_ERROR'],
      });
    }
  }

  // تغيير حالة الفاتورة
  static async updateInvoiceStatus(req: Request, res: Response) {
    try {
      if (!req.user || !req.tenant) {
        return res.status(401).json({
          success: false,
          message: 'المصادقة مطلوبة',
          errors: ['AUTHENTICATION_REQUIRED'],
        });
      }

      const { invoiceId } = req.params;
      const { status } = req.body;

      if (!invoiceId) {
        return res.status(400).json({
          success: false,
          message: 'معرف الفاتورة مطلوب',
          errors: ['INVOICE_ID_REQUIRED'],
        });
      }

      if (!status) {
        return res.status(400).json({
          success: false,
          message: 'حالة الفاتورة مطلوبة',
          errors: ['STATUS_REQUIRED'],
        });
      }

      const validStatuses = ['DRAFT', 'SENT', 'PAID', 'OVERDUE', 'CANCELLED'];
      if (!validStatuses.includes(status)) {
        return res.status(400).json({
          success: false,
          message: 'حالة الفاتورة غير صحيحة',
          errors: ['INVALID_STATUS'],
        });
      }

      const result = await InvoiceService.updateInvoice(req.user.tenantId, invoiceId, { status });

      if (!result.success) {
        return res.status(400).json(result);
      }

      return res.status(200).json(result);
    } catch (error) {
      console.error('Update invoice status controller error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ داخلي في الخادم',
        errors: ['INTERNAL_ERROR'],
      });
    }
  }

  // إرسال فاتورة
  static async sendInvoice(req: Request, res: Response) {
    try {
      if (!req.user || !req.tenant) {
        return res.status(401).json({
          success: false,
          message: 'المصادقة مطلوبة',
          errors: ['AUTHENTICATION_REQUIRED'],
        });
      }

      const { invoiceId } = req.params;

      if (!invoiceId) {
        return res.status(400).json({
          success: false,
          message: 'معرف الفاتورة مطلوب',
          errors: ['INVOICE_ID_REQUIRED'],
        });
      }

      // تحديث حالة الفاتورة إلى مرسلة
      const result = await InvoiceService.updateInvoice(req.user.tenantId, invoiceId, { 
        status: 'SENT',
        sentAt: new Date(),
      });

      if (!result.success) {
        return res.status(400).json(result);
      }

      // TODO: إرسال البريد الإلكتروني للعميل
      // await EmailService.sendInvoice(result.data);

      return res.status(200).json({
        success: true,
        message: 'تم إرسال الفاتورة بنجاح',
        data: result.data,
      });
    } catch (error) {
      console.error('Send invoice controller error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ داخلي في الخادم',
        errors: ['INTERNAL_ERROR'],
      });
    }
  }
}
