import { UserRole } from '@prisma/client';

// JWT Payload interface
export interface JWTPayload {
  userId: string;
  tenantId: string;
  email: string;
  role: UserRole;
  iat?: number;
  exp?: number;
}

// Auth request interface
export interface AuthRequest extends Request {
  user?: JWTPayload;
  tenant?: {
    id: string;
    name: string;
    subdomain: string;
    isActive: boolean;
  };
}

// Login request body
export interface LoginRequest {
  email: string;
  password: string;
  tenantSubdomain?: string;
}

// Register request body
export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  tenantName: string;
  tenantSubdomain: string;
}

// Password reset request
export interface PasswordResetRequest {
  email: string;
  tenantSubdomain?: string;
}

// Password reset confirm
export interface PasswordResetConfirm {
  token: string;
  newPassword: string;
}

// Change password request
export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

// Auth response
export interface AuthResponse {
  success: boolean;
  message: string;
  data?: {
    user: {
      id: string;
      email: string;
      firstName: string;
      lastName: string;
      role: UserRole;
      avatar?: string;
    };
    tenant: {
      id: string;
      name: string;
      subdomain: string;
      logo?: string;
    };
    tokens: {
      accessToken: string;
      refreshToken: string;
      expiresIn: number;
    };
  };
  errors?: string[];
}

// Refresh token request
export interface RefreshTokenRequest {
  refreshToken: string;
}

// User profile update
export interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  phone?: string;
  avatar?: string;
}

// Tenant context
export interface TenantContext {
  id: string;
  name: string;
  subdomain: string;
  domain?: string;
  isActive: boolean;
  settings?: {
    defaultCurrency: string;
    defaultTaxRate: number;
    invoicePrefix: string;
  };
}

// Permission types
export type Permission = 
  | 'users:read'
  | 'users:write'
  | 'users:delete'
  | 'clients:read'
  | 'clients:write'
  | 'clients:delete'
  | 'invoices:read'
  | 'invoices:write'
  | 'invoices:delete'
  | 'invoices:send'
  | 'payments:read'
  | 'payments:write'
  | 'payments:delete'
  | 'reports:read'
  | 'reports:export'
  | 'dashboard:read'
  | 'settings:read'
  | 'settings:write'
  | 'admin:read'
  | 'admin:write';

// Role permissions mapping
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  USER: [
    'clients:read',
    'clients:write',
    'invoices:read',
    'invoices:write',
    'invoices:send',
    'payments:read',
    'reports:read',
    'dashboard:read',
  ],
  ADMIN: [
    'users:read',
    'users:write',
    'clients:read',
    'clients:write',
    'clients:delete',
    'invoices:read',
    'invoices:write',
    'invoices:delete',
    'invoices:send',
    'payments:read',
    'payments:write',
    'payments:delete',
    'reports:read',
    'reports:export',
    'dashboard:read',
    'settings:read',
    'settings:write',
  ],
  SUPER_ADMIN: [
    'users:read',
    'users:write',
    'users:delete',
    'clients:read',
    'clients:write',
    'clients:delete',
    'invoices:read',
    'invoices:write',
    'invoices:delete',
    'invoices:send',
    'payments:read',
    'payments:write',
    'payments:delete',
    'reports:read',
    'reports:export',
    'dashboard:read',
    'settings:read',
    'settings:write',
    'admin:read',
    'admin:write',
  ],
};

// API Error types
export interface APIError {
  code: string;
  message: string;
  field?: string;
}

// Validation error
export interface ValidationError extends APIError {
  field: string;
}

// Auth error codes
export enum AuthErrorCodes {
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  USER_INACTIVE = 'USER_INACTIVE',
  USER_NOT_VERIFIED = 'USER_NOT_VERIFIED',
  TENANT_NOT_FOUND = 'TENANT_NOT_FOUND',
  TENANT_INACTIVE = 'TENANT_INACTIVE',
  INVALID_TOKEN = 'INVALID_TOKEN',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  EMAIL_ALREADY_EXISTS = 'EMAIL_ALREADY_EXISTS',
  SUBDOMAIN_ALREADY_EXISTS = 'SUBDOMAIN_ALREADY_EXISTS',
  WEAK_PASSWORD = 'WEAK_PASSWORD',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
}
