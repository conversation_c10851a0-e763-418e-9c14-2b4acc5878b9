import prisma from '@/config/database';
import { 
  Client, 
  CreateClientRequest, 
  UpdateClientRequest, 
  ClientFilters, 
  ClientWithStats,
  ApiResponse 
} from '@/types/models';

export class ClientService {
  // إنشاء عميل جديد
  static async createClient(
    tenantId: string, 
    data: CreateClientRequest
  ): Promise<ApiResponse<Client>> {
    try {
      // التحقق من عدم وجود عميل بنفس الاسم
      const existingClient = await prisma.client.findFirst({
        where: {
          tenantId,
          name: data.name,
          isActive: true,
        },
      });

      if (existingClient) {
        return {
          success: false,
          message: 'عميل بهذا الاسم موجود بالفعل',
          errors: ['CLIENT_NAME_EXISTS'],
        };
      }

      // إنشاء العميل
      const client = await prisma.client.create({
        data: {
          ...data,
          tenantId,
        },
      });

      return {
        success: true,
        message: 'تم إنشاء العميل بنجاح',
        data: client,
      };
    } catch (error) {
      console.error('Error creating client:', error);
      return {
        success: false,
        message: 'فشل في إنشاء العميل',
        errors: ['CREATE_CLIENT_FAILED'],
      };
    }
  }

  // الحصول على جميع العملاء
  static async getClients(
    tenantId: string, 
    filters: ClientFilters = {}
  ): Promise<ApiResponse<Client[]>> {
    try {
      const { page = 1, limit = 10, search, isActive } = filters;
      const skip = (page - 1) * limit;

      // بناء شروط البحث
      const where: any = { tenantId };
      
      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { phone: { contains: search, mode: 'insensitive' } },
        ];
      }

      // الحصول على العملاء مع العدد الإجمالي
      const [clients, total] = await Promise.all([
        prisma.client.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
        }),
        prisma.client.count({ where }),
      ]);

      return {
        success: true,
        message: 'تم جلب العملاء بنجاح',
        data: clients,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching clients:', error);
      return {
        success: false,
        message: 'فشل في جلب العملاء',
        errors: ['FETCH_CLIENTS_FAILED'],
      };
    }
  }

  // الحصول على عميل واحد
  static async getClientById(
    tenantId: string, 
    clientId: string
  ): Promise<ApiResponse<Client>> {
    try {
      const client = await prisma.client.findFirst({
        where: {
          id: clientId,
          tenantId,
        },
      });

      if (!client) {
        return {
          success: false,
          message: 'العميل غير موجود',
          errors: ['CLIENT_NOT_FOUND'],
        };
      }

      return {
        success: true,
        message: 'تم جلب العميل بنجاح',
        data: client,
      };
    } catch (error) {
      console.error('Error fetching client:', error);
      return {
        success: false,
        message: 'فشل في جلب العميل',
        errors: ['FETCH_CLIENT_FAILED'],
      };
    }
  }

  // تحديث عميل
  static async updateClient(
    tenantId: string, 
    clientId: string, 
    data: UpdateClientRequest
  ): Promise<ApiResponse<Client>> {
    try {
      // التحقق من وجود العميل
      const existingClient = await prisma.client.findFirst({
        where: {
          id: clientId,
          tenantId,
        },
      });

      if (!existingClient) {
        return {
          success: false,
          message: 'العميل غير موجود',
          errors: ['CLIENT_NOT_FOUND'],
        };
      }

      // التحقق من عدم تكرار الاسم (إذا تم تغييره)
      if (data.name && data.name !== existingClient.name) {
        const duplicateClient = await prisma.client.findFirst({
          where: {
            tenantId,
            name: data.name,
            isActive: true,
            id: { not: clientId },
          },
        });

        if (duplicateClient) {
          return {
            success: false,
            message: 'عميل بهذا الاسم موجود بالفعل',
            errors: ['CLIENT_NAME_EXISTS'],
          };
        }
      }

      // تحديث العميل
      const updatedClient = await prisma.client.update({
        where: { id: clientId },
        data,
      });

      return {
        success: true,
        message: 'تم تحديث العميل بنجاح',
        data: updatedClient,
      };
    } catch (error) {
      console.error('Error updating client:', error);
      return {
        success: false,
        message: 'فشل في تحديث العميل',
        errors: ['UPDATE_CLIENT_FAILED'],
      };
    }
  }

  // حذف عميل (إلغاء تفعيل)
  static async deleteClient(
    tenantId: string, 
    clientId: string
  ): Promise<ApiResponse<void>> {
    try {
      // التحقق من وجود العميل
      const existingClient = await prisma.client.findFirst({
        where: {
          id: clientId,
          tenantId,
        },
      });

      if (!existingClient) {
        return {
          success: false,
          message: 'العميل غير موجود',
          errors: ['CLIENT_NOT_FOUND'],
        };
      }

      // التحقق من وجود فواتير مرتبطة
      const invoiceCount = await prisma.invoice.count({
        where: {
          clientId,
          tenantId,
        },
      });

      if (invoiceCount > 0) {
        // إلغاء تفعيل بدلاً من الحذف
        await prisma.client.update({
          where: { id: clientId },
          data: { isActive: false },
        });

        return {
          success: true,
          message: 'تم إلغاء تفعيل العميل بنجاح (يحتوي على فواتير)',
        };
      } else {
        // حذف نهائي إذا لم توجد فواتير
        await prisma.client.delete({
          where: { id: clientId },
        });

        return {
          success: true,
          message: 'تم حذف العميل بنجاح',
        };
      }
    } catch (error) {
      console.error('Error deleting client:', error);
      return {
        success: false,
        message: 'فشل في حذف العميل',
        errors: ['DELETE_CLIENT_FAILED'],
      };
    }
  }

  // الحصول على إحصائيات العميل
  static async getClientStats(
    tenantId: string, 
    clientId: string
  ): Promise<ApiResponse<ClientWithStats>> {
    try {
      // الحصول على العميل
      const client = await prisma.client.findFirst({
        where: {
          id: clientId,
          tenantId,
        },
      });

      if (!client) {
        return {
          success: false,
          message: 'العميل غير موجود',
          errors: ['CLIENT_NOT_FOUND'],
        };
      }

      // حساب الإحصائيات
      const [totalInvoices, paidInvoices, totalAmount, paidAmount] = await Promise.all([
        prisma.invoice.count({
          where: { clientId, tenantId },
        }),
        prisma.invoice.count({
          where: { clientId, tenantId, status: 'PAID' },
        }),
        prisma.invoice.aggregate({
          where: { clientId, tenantId },
          _sum: { totalAmount: true },
        }),
        prisma.invoice.aggregate({
          where: { clientId, tenantId, status: 'PAID' },
          _sum: { totalAmount: true },
        }),
      ]);

      const clientWithStats: ClientWithStats = {
        ...client,
        totalInvoices,
        totalAmount: Number(totalAmount._sum.totalAmount) || 0,
        paidAmount: Number(paidAmount._sum.totalAmount) || 0,
        pendingAmount: (Number(totalAmount._sum.totalAmount) || 0) - (Number(paidAmount._sum.totalAmount) || 0),
      };

      return {
        success: true,
        message: 'تم جلب إحصائيات العميل بنجاح',
        data: clientWithStats,
      };
    } catch (error) {
      console.error('Error fetching client stats:', error);
      return {
        success: false,
        message: 'فشل في جلب إحصائيات العميل',
        errors: ['FETCH_CLIENT_STATS_FAILED'],
      };
    }
  }

  // البحث في العملاء
  static async searchClients(
    tenantId: string, 
    query: string
  ): Promise<ApiResponse<Client[]>> {
    try {
      const clients = await prisma.client.findMany({
        where: {
          tenantId,
          isActive: true,
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { email: { contains: query, mode: 'insensitive' } },
            { phone: { contains: query } },
          ],
        },
        take: 10,
        orderBy: { name: 'asc' },
      });

      return {
        success: true,
        message: 'تم البحث بنجاح',
        data: clients,
      };
    } catch (error) {
      console.error('Error searching clients:', error);
      return {
        success: false,
        message: 'فشل في البحث',
        errors: ['SEARCH_CLIENTS_FAILED'],
      };
    }
  }
}
