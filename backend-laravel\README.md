# نظام إدارة الفواتير - Backend API

## 🚀 إعداد النظام

### 1. متطلبات النظام
- XAMPP (Apache + MySQL + PHP)
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث

### 2. خطوات التثبيت

#### أ. تشغيل XAMPP
1. افتح XAMPP Control Panel
2. شغل Apache
3. شغل MySQL

#### ب. إعداد قاعدة البيانات
1. افتح phpMyAdmin: `http://localhost/phpmyadmin`
2. أنشئ قاعدة بيانات جديدة باسم: `invoice_management`
3. استورد ملف `database/migrations.sql`

#### ج. نسخ الملفات
1. انسخ مجلد `backend-laravel` إلى مجلد `htdocs` في XAMPP
2. تأكد من أن المسار: `C:\xampp\htdocs\backend-laravel`

### 3. اختب<PERSON>ر النظام
- افتح المتصفح واذهب إلى: `http://localhost/backend-laravel`
- يجب أن تظهر صفحة API Documentation

## 📋 API Endpoints

### 🔐 المصادقة
- `POST /api/auth/login` - تسجيل الدخول
- `POST /api/auth/register` - إنشاء حساب جديد
- `GET /api/auth/profile` - جلب بيانات المستخدم
- `POST /api/auth/logout` - تسجيل الخروج

### 👥 العملاء
- `GET /api/clients` - جلب قائمة العملاء
- `GET /api/clients/{id}` - جلب عميل محدد
- `POST /api/clients` - إضافة عميل جديد
- `PUT /api/clients/{id}` - تحديث عميل
- `DELETE /api/clients/{id}` - حذف عميل

### 📊 لوحة التحكم
- `GET /api/dashboard/stats` - إحصائيات لوحة التحكم

## 🔧 إعدادات قاعدة البيانات

```php
// في ملف config/database.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'invoice_management');
define('DB_USER', 'root');
define('DB_PASS', '');
```

## 📝 بيانات تجريبية

### حساب المدير
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: admin123

### عملاء تجريبيون
- شركة الأمل للتجارة
- مؤسسة النور
- شركة الفجر للمقاولات
- مكتب الإبداع للاستشارات
- شركة التقنية المتقدمة

## 🛠️ استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات
1. تأكد من تشغيل MySQL في XAMPP
2. تحقق من إعدادات قاعدة البيانات في `config/database.php`
3. تأكد من وجود قاعدة البيانات `invoice_management`

### خطأ CORS
1. تأكد من وجود ملف `.htaccess`
2. تحقق من أن Apache يدعم mod_rewrite
3. تأكد من أن Frontend يعمل على `http://localhost:3000`

### خطأ في الصلاحيات
1. تأكد من صلاحيات الكتابة على مجلد `backend-laravel`
2. تحقق من إعدادات PHP في XAMPP

## 📁 هيكل المشروع

```
backend-laravel/
├── api/
│   ├── auth.php          # API المصادقة
│   ├── clients.php       # API العملاء
│   └── dashboard.php     # API لوحة التحكم
├── config/
│   └── database.php      # إعدادات قاعدة البيانات
├── database/
│   └── migrations.sql    # جداول قاعدة البيانات
├── .htaccess            # إعدادات Apache
├── index.php            # نقطة الدخول الرئيسية
└── README.md            # هذا الملف
```

## 🔒 الأمان

- تشفير كلمات المرور باستخدام `password_hash()`
- JWT tokens للمصادقة
- حماية من SQL Injection باستخدام Prepared Statements
- تحقق من صحة البيانات المدخلة
- CORS headers محددة

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملفات الأخطاء في XAMPP
2. تأكد من تشغيل جميع الخدمات المطلوبة
3. راجع إعدادات قاعدة البيانات

---

تم تطوير هذا النظام بعناية لإدارة الفواتير بكفاءة وأمان عالي.
