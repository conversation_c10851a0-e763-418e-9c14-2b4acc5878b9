import type { Metadata } from 'next';



export const metadata: Metadata = {
  title: 'نظام إدارة الفواتير - Web Invoice SaaS',
  description: 'نظام شامل لإدارة الفواتير والعملاء مع دعم Multi-Tenant',
  keywords: ['فواتير', 'إدارة', 'محاسبة', 'SaaS', 'عملاء'],
  authors: [{ name: 'Web Invoice Team' }],
  creator: 'Web Invoice Team',
  publisher: 'Web Invoice SaaS',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('http://localhost:3000'),
  openGraph: {
    title: 'نظام إدارة الفواتير',
    description: 'نظام شامل لإدارة الفواتير والعملاء',
    url: 'http://localhost:3000',
    siteName: 'Web Invoice SaaS',
    locale: 'ar_SA',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'نظام إدارة الفواتير',
    description: 'نظام شامل لإدارة الفواتير والعملاء',
  },
  robots: {
    index: false,
    follow: false,
    googleBot: {
      index: false,
      follow: false,
    },
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="ar" dir="rtl" className={inter.variable}>
      <body className="font-sans antialiased bg-gray-50 text-gray-900">
        <Providers>
          {children}
          <Toaster
            position="top-center"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
                fontFamily: 'inherit',
                fontSize: '14px',
                borderRadius: '8px',
                padding: '12px 16px',
              },
              success: {
                style: {
                  background: '#10b981',
                },
                iconTheme: {
                  primary: '#fff',
                  secondary: '#10b981',
                },
              },
              error: {
                style: {
                  background: '#ef4444',
                },
                iconTheme: {
                  primary: '#fff',
                  secondary: '#ef4444',
                },
              },
              loading: {
                style: {
                  background: '#3b82f6',
                },
                iconTheme: {
                  primary: '#fff',
                  secondary: '#3b82f6',
                },
              },
            }}
          />
        </Providers>
      </body>
    </html>
  );
}
