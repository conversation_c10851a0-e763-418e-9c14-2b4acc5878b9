<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الفواتير - Web Invoice SaaS</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            text-align: right;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.2em;
        }

        .status {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .status.success {
            border-right: 5px solid #28a745;
        }

        .status.error {
            border-right: 5px solid #dc3545;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn.danger {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        }

        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .login-form {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: bold;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .features {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .features h2 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .features ul {
            list-style: none;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .features li {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-right: 4px solid #667eea;
        }

        .footer {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 30px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🚀 نظام إدارة الفواتير</h1>
            <p>Web Invoice SaaS - نظام شامل لإدارة الفواتير والعملاء</p>
        </div>

        <!-- Server Status -->
        <div id="serverStatus" class="status">
            <h3>🔄 جاري فحص حالة الخادم...</h3>
        </div>

        <!-- Login Form -->
        <div class="login-form">
            <h2>🔐 تسجيل الدخول</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="email">البريد الإلكتروني:</label>
                    <input type="email" id="email" value="<EMAIL>" required>
                </div>
                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" value="admin123" required>
                </div>
                <button type="submit" class="btn">تسجيل الدخول</button>
            </form>
            <div id="loginResult" class="result" style="display: none;"></div>
        </div>

        <!-- API Testing -->
        <div class="grid">
            <div class="card">
                <h3>🏥 فحص صحة الخادم</h3>
                <p>اختبار حالة الخادم والاتصال</p>
                <button class="btn" onclick="testHealth()">فحص الصحة</button>
                <div id="healthResult" class="result" style="display: none;"></div>
            </div>

            <div class="card">
                <h3>📋 معلومات API</h3>
                <p>الحصول على معلومات شاملة عن النظام</p>
                <button class="btn secondary" onclick="getApiInfo()">معلومات API</button>
                <div id="apiResult" class="result" style="display: none;"></div>
            </div>

            <div class="card">
                <h3>🗄️ اختبار قاعدة البيانات</h3>
                <p>فحص الاتصال بقاعدة البيانات</p>
                <button class="btn" onclick="testDatabase()">اختبار قاعدة البيانات</button>
                <div id="dbResult" class="result" style="display: none;"></div>
            </div>

            <div class="card">
                <h3>👥 قائمة العملاء</h3>
                <p>عرض العملاء التجريبيين</p>
                <button class="btn secondary" onclick="getClients()">عرض العملاء</button>
                <div id="clientsResult" class="result" style="display: none;"></div>
            </div>

            <div class="card">
                <h3>📄 قائمة الفواتير</h3>
                <p>عرض الفواتير التجريبية</p>
                <button class="btn" onclick="getInvoices()">عرض الفواتير</button>
                <div id="invoicesResult" class="result" style="display: none;"></div>
            </div>

            <div class="card">
                <h3>📊 إحصائيات لوحة التحكم</h3>
                <p>عرض إحصائيات النظام</p>
                <button class="btn secondary" onclick="getDashboardStats()">عرض الإحصائيات</button>
                <div id="dashboardResult" class="result" style="display: none;"></div>
            </div>
        </div>

        <!-- Features -->
        <div class="features">
            <h2>✨ ميزات النظام</h2>
            <ul>
                <li>💼 إدارة العملاء والمعلومات</li>
                <li>📄 إنشاء وإدارة الفواتير</li>
                <li>📊 لوحة تحكم تفاعلية</li>
                <li>🏢 دعم Multi-Tenant</li>
                <li>🔐 مصادقة JWT آمنة</li>
                <li>🌐 دعم اللغة العربية</li>
                <li>🗄️ قاعدة بيانات MySQL</li>
                <li>📱 تصميم متجاوب</li>
            </ul>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>تم تطوير هذا النظام بـ ❤️ لخدمة المجتمع العربي</p>
            <p>Web Invoice SaaS © 2024</p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001';

        // Check server status on page load
        window.addEventListener('load', checkServerStatus);

        async function checkServerStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                document.getElementById('serverStatus').innerHTML = `
                    <h3>✅ الخادم يعمل بنجاح!</h3>
                    <p>الحالة: ${data.status} | الإصدار: ${data.version} | الوقت: ${new Date(data.timestamp).toLocaleString('ar')}</p>
                `;
                document.getElementById('serverStatus').className = 'status success';
            } catch (error) {
                document.getElementById('serverStatus').innerHTML = `
                    <h3>❌ فشل في الاتصال بالخادم</h3>
                    <p>تأكد من تشغيل الخادم على المنفذ 5000</p>
                `;
                document.getElementById('serverStatus').className = 'status error';
            }
        }

        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                document.getElementById('loginResult').style.display = 'block';
                document.getElementById('loginResult').textContent = JSON.stringify(data, null, 2);
                
                if (data.success) {
                    document.getElementById('loginResult').style.background = '#d4edda';
                    document.getElementById('loginResult').style.borderColor = '#c3e6cb';
                } else {
                    document.getElementById('loginResult').style.background = '#f8d7da';
                    document.getElementById('loginResult').style.borderColor = '#f5c6cb';
                }
            } catch (error) {
                document.getElementById('loginResult').style.display = 'block';
                document.getElementById('loginResult').textContent = `خطأ: ${error.message}`;
                document.getElementById('loginResult').style.background = '#f8d7da';
                document.getElementById('loginResult').style.borderColor = '#f5c6cb';
            }
        });

        async function testHealth() {
            await makeRequest('/health', 'healthResult');
        }

        async function getApiInfo() {
            await makeRequest('/api', 'apiResult');
        }

        async function testDatabase() {
            await makeRequest('/api/test-db', 'dbResult');
        }

        async function getClients() {
            await makeRequest('/api/clients', 'clientsResult');
        }

        async function getInvoices() {
            await makeRequest('/api/invoices', 'invoicesResult');
        }

        async function getDashboardStats() {
            await makeRequest('/api/dashboard/stats', 'dashboardResult');
        }

        async function makeRequest(endpoint, resultElementId) {
            const resultElement = document.getElementById(resultElementId);
            
            try {
                resultElement.style.display = 'block';
                resultElement.textContent = 'جاري التحميل...';
                resultElement.style.background = '#fff3cd';
                resultElement.style.borderColor = '#ffeaa7';
                
                const response = await fetch(`${API_BASE}${endpoint}`);
                const data = await response.json();
                
                resultElement.textContent = JSON.stringify(data, null, 2);
                
                if (response.ok && data.success !== false) {
                    resultElement.style.background = '#d4edda';
                    resultElement.style.borderColor = '#c3e6cb';
                } else {
                    resultElement.style.background = '#f8d7da';
                    resultElement.style.borderColor = '#f5c6cb';
                }
            } catch (error) {
                resultElement.style.display = 'block';
                resultElement.textContent = `خطأ في الاتصال: ${error.message}`;
                resultElement.style.background = '#f8d7da';
                resultElement.style.borderColor = '#f5c6cb';
            }
        }
    </script>
</body>
</html>
