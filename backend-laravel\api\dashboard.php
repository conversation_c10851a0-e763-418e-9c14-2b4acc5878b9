<?php
require_once '../config/database.php';

setCorsHeaders();

class DashboardAPI {
    private $db;
    private $tenant_id;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        
        // For demo purposes, we'll use tenant_id = 1
        // In production, this should come from JW<PERSON> token
        $this->tenant_id = 1;
    }

    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $path_parts = explode('/', trim($path, '/'));
        
        if (!isset($path_parts[2])) {
            errorResponse('نقطة نهاية غير صحيحة', 400);
        }
        
        $endpoint = $path_parts[2];
        
        switch ($endpoint) {
            case 'stats':
                if ($method === 'GET') {
                    $this->getDashboardStats();
                } else {
                    errorResponse('طريقة غير مدعومة', 405);
                }
                break;
            default:
                errorResponse('نقطة نهاية غير موجودة', 404);
        }
    }

    public function getDashboardStats() {
        try {
            // Get basic counts
            $stats = [];
            
            // Total clients
            $sql = "SELECT COUNT(*) as count FROM clients WHERE tenant_id = :tenant_id AND is_active = 1";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':tenant_id', $this->tenant_id);
            $stmt->execute();
            $stats['totalClients'] = (int)$stmt->fetch()['count'];
            
            // Total invoices
            $sql = "SELECT COUNT(*) as count FROM invoices WHERE tenant_id = :tenant_id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':tenant_id', $this->tenant_id);
            $stmt->execute();
            $stats['totalInvoices'] = (int)$stmt->fetch()['count'];
            
            // Total revenue (paid invoices)
            $sql = "SELECT COALESCE(SUM(total_amount), 0) as total FROM invoices WHERE tenant_id = :tenant_id AND status = 'PAID'";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':tenant_id', $this->tenant_id);
            $stmt->execute();
            $stats['totalRevenue'] = (float)$stmt->fetch()['total'];
            
            // This month revenue
            $sql = "SELECT COALESCE(SUM(total_amount), 0) as total FROM invoices 
                    WHERE tenant_id = :tenant_id AND status = 'PAID' 
                    AND MONTH(created_at) = MONTH(CURRENT_DATE()) 
                    AND YEAR(created_at) = YEAR(CURRENT_DATE())";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':tenant_id', $this->tenant_id);
            $stmt->execute();
            $stats['thisMonthRevenue'] = (float)$stmt->fetch()['total'];
            
            // Last month revenue
            $sql = "SELECT COALESCE(SUM(total_amount), 0) as total FROM invoices 
                    WHERE tenant_id = :tenant_id AND status = 'PAID' 
                    AND MONTH(created_at) = MONTH(CURRENT_DATE() - INTERVAL 1 MONTH) 
                    AND YEAR(created_at) = YEAR(CURRENT_DATE() - INTERVAL 1 MONTH)";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':tenant_id', $this->tenant_id);
            $stmt->execute();
            $stats['lastMonthRevenue'] = (float)$stmt->fetch()['total'];
            
            // Pending amount (sent but not paid)
            $sql = "SELECT COALESCE(SUM(total_amount), 0) as total FROM invoices 
                    WHERE tenant_id = :tenant_id AND status IN ('SENT', 'OVERDUE')";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':tenant_id', $this->tenant_id);
            $stmt->execute();
            $stats['pendingAmount'] = (float)$stmt->fetch()['total'];
            
            // Overdue invoices count
            $sql = "SELECT COUNT(*) as count FROM invoices 
                    WHERE tenant_id = :tenant_id AND status = 'OVERDUE'";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':tenant_id', $this->tenant_id);
            $stmt->execute();
            $stats['overdueInvoices'] = (int)$stmt->fetch()['count'];
            
            // Paid invoices count
            $sql = "SELECT COUNT(*) as count FROM invoices WHERE tenant_id = :tenant_id AND status = 'PAID'";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':tenant_id', $this->tenant_id);
            $stmt->execute();
            $stats['paidInvoices'] = (int)$stmt->fetch()['count'];
            
            // Recent invoices
            $sql = "SELECT i.*, c.name as client_name, c.email as client_email, c.phone as client_phone
                    FROM invoices i
                    LEFT JOIN clients c ON i.client_id = c.id
                    WHERE i.tenant_id = :tenant_id
                    ORDER BY i.created_at DESC
                    LIMIT 5";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':tenant_id', $this->tenant_id);
            $stmt->execute();
            $recent_invoices = $stmt->fetchAll();
            
            $stats['recentInvoices'] = array_map(function($invoice) {
                return [
                    'id' => (string)$invoice['id'],
                    'invoiceNumber' => $invoice['invoice_number'],
                    'clientId' => (string)$invoice['client_id'],
                    'client' => [
                        'id' => (string)$invoice['client_id'],
                        'name' => $invoice['client_name'],
                        'email' => $invoice['client_email'],
                        'phone' => $invoice['client_phone'],
                        'isActive' => true,
                        'createdAt' => $invoice['created_at'],
                        'updatedAt' => $invoice['updated_at']
                    ],
                    'issueDate' => $invoice['issue_date'],
                    'dueDate' => $invoice['due_date'],
                    'status' => $invoice['status'],
                    'subtotal' => (float)$invoice['subtotal'],
                    'taxRate' => (float)$invoice['tax_rate'],
                    'taxAmount' => (float)$invoice['tax_amount'],
                    'totalAmount' => (float)$invoice['total_amount'],
                    'currency' => $invoice['currency'],
                    'notes' => $invoice['notes'],
                    'createdAt' => $invoice['created_at'],
                    'updatedAt' => $invoice['updated_at']
                ];
            }, $recent_invoices);
            
            // Top clients by revenue
            $sql = "SELECT c.*, 
                           COUNT(i.id) as total_invoices,
                           COALESCE(SUM(CASE WHEN i.status = 'PAID' THEN i.total_amount ELSE 0 END), 0) as paid_amount,
                           COALESCE(SUM(i.total_amount), 0) as total_amount
                    FROM clients c
                    LEFT JOIN invoices i ON c.id = i.client_id
                    WHERE c.tenant_id = :tenant_id AND c.is_active = 1
                    GROUP BY c.id
                    ORDER BY paid_amount DESC
                    LIMIT 5";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':tenant_id', $this->tenant_id);
            $stmt->execute();
            $top_clients = $stmt->fetchAll();
            
            $stats['topClients'] = array_map(function($client) {
                return [
                    'id' => (string)$client['id'],
                    'name' => $client['name'],
                    'totalInvoices' => (int)$client['total_invoices'],
                    'totalAmount' => (float)$client['total_amount'],
                    'paidAmount' => (float)$client['paid_amount']
                ];
            }, $top_clients);
            
            successResponse($stats, 'تم جلب إحصائيات لوحة التحكم بنجاح');
            
        } catch (Exception $e) {
            errorResponse('خطأ في جلب الإحصائيات: ' . $e->getMessage(), 500);
        }
    }
}

// Handle the request
try {
    $api = new DashboardAPI();
    $api->handleRequest();
} catch (Exception $e) {
    errorResponse('خطأ في الخادم: ' . $e->getMessage(), 500);
}
?>
