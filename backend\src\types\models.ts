// نماذج البيانات الأساسية
// Basic Data Models

export interface Tenant {
  id: string;
  name: string;
  domain?: string;
  settings?: any;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface User {
  id: string;
  tenantId: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: 'USER' | 'ADMIN' | 'SUPER_ADMIN';
  isActive: boolean;
  lastLoginAt?: Date;
  emailVerifiedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface Client {
  id: string;
  tenantId: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  taxNumber?: string;
  notes?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Invoice {
  id: string;
  tenantId: string;
  clientId: string;
  userId: string;
  invoiceNumber: string;
  status: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED';
  issueDate: Date;
  dueDate: Date;
  subtotal: number;
  taxRate: number;
  taxAmount: number;
  discountRate: number;
  discountAmount: number;
  totalAmount: number;
  currency: string;
  notes?: string;
  terms?: string;
  paidAt?: Date;
  sentAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface InvoiceItem {
  id: string;
  invoiceId: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Payment {
  id: string;
  tenantId: string;
  invoiceId: string;
  amount: number;
  paymentMethod: 'CASH' | 'BANK_TRANSFER' | 'CREDIT_CARD' | 'PAYPAL' | 'STRIPE' | 'OTHER';
  paymentDate: Date;
  reference?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TenantSettings {
  id: string;
  tenantId: string;
  invoicePrefix: string;
  invoiceNumbering: 'SEQUENTIAL' | 'RANDOM' | 'CUSTOM';
  nextInvoiceNumber: number;
  defaultDueDays: number;
  defaultCurrency: string;
  defaultTaxRate: number;
  companyLogo?: string;
  companyAddress?: string;
  companyPhone?: string;
  companyEmail?: string;
  companyWebsite?: string;
  emailSignature?: string;
  invoiceFooter?: string;
  invoiceTerms?: string;
  allowOnlinePayment: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// DTOs للطلبات والاستجابات
// Request/Response DTOs

export interface CreateClientRequest {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  taxNumber?: string;
  notes?: string;
}

export interface UpdateClientRequest extends Partial<CreateClientRequest> {
  isActive?: boolean;
}

export interface CreateInvoiceRequest {
  clientId: string;
  title?: string;
  description?: string;
  dueDate: string; // ISO date string
  currency?: string;
  taxRate?: number;
  discountRate?: number;
  notes?: string;
  terms?: string;
  items: CreateInvoiceItemRequest[];
}

export interface CreateInvoiceItemRequest {
  description: string;
  quantity: number;
  unitPrice: number;
}

export interface UpdateInvoiceRequest extends Partial<CreateInvoiceRequest> {
  status?: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED';
}

export interface CreatePaymentRequest {
  invoiceId: string;
  amount: number;
  paymentMethod: 'CASH' | 'BANK_TRANSFER' | 'CREDIT_CARD' | 'PAYPAL' | 'STRIPE' | 'OTHER';
  paymentDate: string; // ISO date string
  reference?: string;
  notes?: string;
}

// استجابات API
// API Responses

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: string[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface InvoiceWithDetails extends Invoice {
  client: Client;
  user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
  items: InvoiceItem[];
  payments: Payment[];
}

export interface ClientWithStats extends Client {
  totalInvoices: number;
  totalAmount: number;
  paidAmount: number;
  pendingAmount: number;
}

export interface DashboardStats {
  totalClients: number;
  totalInvoices: number;
  paidInvoices: number;
  overdueInvoices: number;
  totalRevenue: number;
  pendingAmount: number;
  thisMonthRevenue: number;
  lastMonthRevenue: number;
  recentInvoices: InvoiceWithDetails[];
  topClients: ClientWithStats[];
}

// معاملات الاستعلام
// Query Parameters

export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface InvoiceFilters extends PaginationParams {
  status?: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED';
  clientId?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
}

export interface ClientFilters extends PaginationParams {
  search?: string;
  isActive?: boolean;
}

export interface PaymentFilters extends PaginationParams {
  invoiceId?: string;
  paymentMethod?: string;
  startDate?: string;
  endDate?: string;
}

// أنواع الأخطاء
// Error Types

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface BusinessError {
  code: string;
  message: string;
  details?: any;
}

// أنواع الأحداث
// Event Types

export interface ActivityLog {
  id: string;
  tenantId: string;
  userId?: string;
  action: string;
  entityType: string;
  entityId?: string;
  description: string;
  metadata?: any;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
}

export interface Notification {
  id: string;
  tenantId: string;
  userId: string;
  type: string;
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  readAt?: Date;
  createdAt: Date;
}
