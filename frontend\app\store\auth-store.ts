'use client';

import { useEffect, useState } from 'react';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiClient, type User, type Tenant } from '../lib/api-client';

// Auth State Interface
interface AuthState {
  // State
  user: User | null;
  tenant: Tenant | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (email: string, password: string) => Promise<boolean>;
  register: (userData: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    companyName: string;
    phone?: string;
  }) => Promise<boolean>;
  logout: () => void;
  getProfile: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

// Create Auth Store
const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial State
      user: null,
      tenant: null,
      token: null,
      isAuthenticated: false,
      isLoading: true, // Start with loading true, will be set to false after rehydration
      error: null,

      // Actions
      login: async (email: string, password: string) => {
        try {
          set({ isLoading: true, error: null });

          const response = await apiClient.login(email, password);
          
          if (response.success && response.data) {
            const { user, tenant, token } = response.data;
            
            // Set token in API client
            apiClient.setToken(token);
            
            // Update store state
            set({
              user,
              tenant,
              token,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
            
            return true;
          } else {
            set({
              isLoading: false,
              error: response.message || 'فشل في تسجيل الدخول',
            });
            return false;
          }
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'حدث خطأ أثناء تسجيل الدخول',
          });
          return false;
        }
      },

      register: async (userData) => {
        try {
          set({ isLoading: true, error: null });

          const response = await apiClient.register(userData);
          
          if (response.success && response.data) {
            const { user, tenant, token } = response.data;
            
            // Set token in API client
            apiClient.setToken(token);
            
            // Update store state
            set({
              user,
              tenant,
              token,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
            
            return true;
          } else {
            set({
              isLoading: false,
              error: response.message || 'فشل في إنشاء الحساب',
            });
            return false;
          }
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'حدث خطأ أثناء إنشاء الحساب',
          });
          return false;
        }
      },

      logout: () => {
        // Clear token from API client
        apiClient.clearToken();
        
        // Reset store state
        set({
          user: null,
          tenant: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
        
        // Call logout API (optional, for server-side cleanup)
        apiClient.logout().catch(console.error);
      },

      getProfile: async () => {
        try {
          const { token } = get();
          
          if (!token) {
            return;
          }

          set({ isLoading: true });

          const response = await apiClient.getProfile();
          
          if (response.success && response.data) {
            const { user, tenant } = response.data;
            
            set({
              user,
              tenant,
              isLoading: false,
              error: null,
            });
          } else {
            // If profile fetch fails, logout user
            get().logout();
          }
        } catch (error: any) {
          console.error('Profile fetch error:', error);
          // If profile fetch fails, logout user
          get().logout();
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        tenant: state.tenant,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        // Set token in API client when rehydrating from storage
        if (state?.token) {
          apiClient.setToken(state.token);
        }
        // Set loading to false after rehydration
        return (state: AuthState | undefined) => {
          if (state) {
            state.isLoading = false;
          }
        };
      },
    }
  )
);

// Custom hooks for easier usage
export const useAuth = () => {
  const store = useAuthStore();
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    // Mark as hydrated after first render
    setHydrated(true);

    // If we have a token but not authenticated, set authenticated to true
    if (store.token && !store.isAuthenticated) {
      useAuthStore.setState({ isAuthenticated: true, isLoading: false });
    } else if (!store.token) {
      // If no token, make sure we're not loading
      useAuthStore.setState({ isLoading: false });
    }
  }, [store.token, store.isAuthenticated]);

  // Return loading true until hydrated
  return {
    ...store,
    isLoading: !hydrated || store.isLoading
  };
};

export const useAuthActions = () => {
  const { login, register, logout, getProfile, clearError, setLoading } = useAuthStore();
  return { login, register, logout, getProfile, clearError, setLoading };
};

// Export the store for direct access if needed
export default useAuthStore;
