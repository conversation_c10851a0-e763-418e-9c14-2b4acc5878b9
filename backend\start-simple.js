// Simple Node.js server for testing
const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'نظام إدارة الفواتير يعمل بنجاح!',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: 'development'
  });
});

// API info endpoint
app.get('/api', (req, res) => {
  res.json({
    name: 'نظام إدارة الفواتير - Web Invoice SaaS',
    version: '1.0.0',
    description: 'نظام شامل لإدارة الفواتير والعملاء مع دعم Multi-Tenant',
    status: 'running',
    endpoints: {
      health: '/health',
      auth: '/api/auth (قريباً)',
      clients: '/api/clients (قريباً)',
      invoices: '/api/invoices (قريباً)',
      dashboard: '/api/dashboard (قريباً)',
    },
    database: {
      type: 'MySQL',
      status: 'متصل',
      name: 'web_invoice_saas'
    },
    features: [
      'إدارة العملاء',
      'إدارة الفواتير',
      'لوحة التحكم',
      'Multi-Tenant Support',
      'مصادقة JWT',
      'دعم اللغة العربية'
    ],
    documentation: {
      setup: 'docs/XAMPP-SETUP.md',
      database: 'docs/database-import-guide.md',
      api: 'docs/api-documentation.md'
    },
    testAccount: {
      email: '<EMAIL>',
      password: 'admin123',
      role: 'SUPER_ADMIN'
    }
  });
});

// Test database connection endpoint
app.get('/api/test-db', async (req, res) => {
  try {
    // This would normally test the actual database connection
    res.json({
      success: true,
      message: 'اتصال قاعدة البيانات ناجح',
      database: 'web_invoice_saas',
      host: 'localhost:3306',
      user: 'invoice_app'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'فشل في الاتصال بقاعدة البيانات',
      error: error.message
    });
  }
});

// Mock auth endpoints
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  if (email === '<EMAIL>' && password === 'admin123') {
    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      data: {
        user: {
          id: 'demo-user-1',
          email: '<EMAIL>',
          firstName: 'أحمد',
          lastName: 'المدير',
          role: 'SUPER_ADMIN'
        },
        token: 'mock-jwt-token-for-testing',
        expiresIn: '7d'
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'بيانات تسجيل الدخول غير صحيحة',
      errors: ['INVALID_CREDENTIALS']
    });
  }
});

// Mock clients endpoint
app.get('/api/clients', (req, res) => {
  res.json({
    success: true,
    message: 'تم جلب العملاء بنجاح',
    data: [
      {
        id: 'demo-client-1',
        name: 'شركة البناء الحديث',
        email: '<EMAIL>',
        phone: '+966112345678',
        city: 'الرياض',
        country: 'المملكة العربية السعودية'
      },
      {
        id: 'demo-client-2',
        name: 'مؤسسة التجارة الإلكترونية',
        email: '<EMAIL>',
        phone: '+966113456789',
        city: 'الرياض',
        country: 'المملكة العربية السعودية'
      }
    ],
    pagination: {
      page: 1,
      limit: 10,
      total: 2,
      totalPages: 1
    }
  });
});

// Mock invoices endpoint
app.get('/api/invoices', (req, res) => {
  res.json({
    success: true,
    message: 'تم جلب الفواتير بنجاح',
    data: [
      {
        id: 'demo-invoice-1',
        invoiceNumber: 'INV-2024-001',
        status: 'SENT',
        totalAmount: 11500.00,
        currency: 'SAR',
        issueDate: '2024-01-15',
        dueDate: '2024-02-14',
        client: {
          name: 'شركة البناء الحديث',
          email: '<EMAIL>'
        }
      },
      {
        id: 'demo-invoice-2',
        invoiceNumber: 'INV-2024-002',
        status: 'PAID',
        totalAmount: 8250.00,
        currency: 'SAR',
        issueDate: '2024-01-20',
        dueDate: '2024-02-19',
        client: {
          name: 'مؤسسة التجارة الإلكترونية',
          email: '<EMAIL>'
        }
      }
    ],
    pagination: {
      page: 1,
      limit: 10,
      total: 2,
      totalPages: 1
    }
  });
});

// Mock dashboard stats endpoint
app.get('/api/dashboard/stats', (req, res) => {
  res.json({
    success: true,
    message: 'تم جلب إحصائيات لوحة التحكم بنجاح',
    data: {
      totalClients: 3,
      totalInvoices: 3,
      paidInvoices: 1,
      overdueInvoices: 1,
      totalRevenue: 8250.00,
      pendingAmount: 11500.00,
      thisMonthRevenue: 8250.00,
      lastMonthRevenue: 0,
      recentInvoices: [
        {
          id: 'demo-invoice-1',
          invoiceNumber: 'INV-2024-001',
          status: 'SENT',
          totalAmount: 11500.00,
          client: { name: 'شركة البناء الحديث' }
        }
      ],
      topClients: [
        {
          id: 'demo-client-1',
          name: 'شركة البناء الحديث',
          totalAmount: 11500.00,
          totalInvoices: 1
        }
      ]
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'المسار غير موجود',
    error: 'NOT_FOUND',
    availableEndpoints: [
      'GET /health',
      'GET /api',
      'GET /api/test-db',
      'POST /api/auth/login',
      'GET /api/clients',
      'GET /api/invoices',
      'GET /api/dashboard/stats'
    ]
  });
});

// Error handler
app.use((error, req, res, next) => {
  console.error('Server Error:', error);
  res.status(500).json({
    success: false,
    message: 'خطأ داخلي في الخادم',
    error: 'INTERNAL_SERVER_ERROR'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 نظام إدارة الفواتير يعمل على المنفذ ${PORT}`);
  console.log(`📡 API Server: http://localhost:${PORT}`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
  console.log(`📋 API Info: http://localhost:${PORT}/api`);
  console.log(`🧪 Test Login: POST http://localhost:${PORT}/api/auth/login`);
  console.log(`   Email: <EMAIL>`);
  console.log(`   Password: admin123`);
  console.log('');
  console.log('✨ النظام جاهز للاستخدام!');
});

module.exports = app;
