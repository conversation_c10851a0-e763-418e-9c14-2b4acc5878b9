// Simple API Client for Invoice Management System
const API_BASE_URL = 'http://localhost:5001';

// Types
export interface User {
  id: string;
  tenantId: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: 'USER' | 'ADMIN' | 'SUPER_ADMIN';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Tenant {
  id: string;
  name: string;
  domain?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  data?: {
    user: User;
    tenant: Tenant;
    token: string;
    refreshToken: string;
    expiresIn: string;
  };
  errors?: string[];
}

export interface Client {
  id: string;
  tenantId: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  taxNumber?: string;
  notes?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Invoice {
  id: string;
  tenantId: string;
  clientId: string;
  userId: string;
  invoiceNumber: string;
  status: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED';
  issueDate: string;
  dueDate: string;
  subtotal: number;
  taxRate: number;
  taxAmount: number;
  discountRate: number;
  discountAmount: number;
  totalAmount: number;
  currency: string;
  notes?: string;
  terms?: string;
  paidAt?: string;
  sentAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface InvoiceItem {
  id: string;
  invoiceId: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export interface InvoiceWithDetails extends Invoice {
  client: Client;
  user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
  items: InvoiceItem[];
  payments: any[];
}

export interface DashboardStats {
  totalClients: number;
  totalInvoices: number;
  paidInvoices: number;
  overdueInvoices: number;
  totalRevenue: number;
  pendingAmount: number;
  thisMonthRevenue: number;
  lastMonthRevenue: number;
  recentInvoices: InvoiceWithDetails[];
  topClients: any[];
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: string[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// API Client Class
class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
    this.loadToken();
  }

  private loadToken() {
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('auth_token');
    }
  }

  private saveToken(token: string) {
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token);
      this.token = token;
    }
  }

  private removeToken() {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
      this.token = null;
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API Request Error:', error);
      throw error;
    }
  }

  // Auth methods
  async login(email: string, password: string): Promise<AuthResponse> {
    const response = await this.request<AuthResponse['data']>('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });

    if (response.success && response.data?.token) {
      this.saveToken(response.data.token);
    }

    return response as AuthResponse;
  }

  async register(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    companyName: string;
    phone?: string;
  }): Promise<AuthResponse> {
    return this.request<AuthResponse['data']>('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    }) as Promise<AuthResponse>;
  }

  async getProfile(): Promise<ApiResponse<User>> {
    return this.request<User>('/api/auth/profile');
  }

  logout() {
    this.removeToken();
  }

  isAuthenticated(): boolean {
    return !!this.token;
  }

  // Dashboard methods
  async getDashboardStats(): Promise<ApiResponse<DashboardStats>> {
    return this.request<DashboardStats>('/api/dashboard/stats');
  }

  // Clients methods
  async getClients(params?: any): Promise<ApiResponse<Client[]>> {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return this.request<Client[]>(`/api/clients${queryString}`);
  }

  async getClient(id: string): Promise<ApiResponse<Client>> {
    return this.request<Client>(`/api/clients/${id}`);
  }

  async createClient(clientData: Partial<Client>): Promise<ApiResponse<Client>> {
    return this.request<Client>('/api/clients', {
      method: 'POST',
      body: JSON.stringify(clientData),
    });
  }

  async updateClient(id: string, clientData: Partial<Client>): Promise<ApiResponse<Client>> {
    return this.request<Client>(`/api/clients/${id}`, {
      method: 'PUT',
      body: JSON.stringify(clientData),
    });
  }

  async deleteClient(id: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/api/clients/${id}`, {
      method: 'DELETE',
    });
  }

  // Invoices methods
  async getInvoices(params?: any): Promise<ApiResponse<InvoiceWithDetails[]>> {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return this.request<InvoiceWithDetails[]>(`/api/invoices${queryString}`);
  }

  async getInvoice(id: string): Promise<ApiResponse<InvoiceWithDetails>> {
    return this.request<InvoiceWithDetails>(`/api/invoices/${id}`);
  }

  async createInvoice(invoiceData: any): Promise<ApiResponse<InvoiceWithDetails>> {
    return this.request<InvoiceWithDetails>('/api/invoices', {
      method: 'POST',
      body: JSON.stringify(invoiceData),
    });
  }

  async updateInvoice(id: string, invoiceData: any): Promise<ApiResponse<InvoiceWithDetails>> {
    return this.request<InvoiceWithDetails>(`/api/invoices/${id}`, {
      method: 'PUT',
      body: JSON.stringify(invoiceData),
    });
  }

  async deleteInvoice(id: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/api/invoices/${id}`, {
      method: 'DELETE',
    });
  }

  async sendInvoice(id: string): Promise<ApiResponse<InvoiceWithDetails>> {
    return this.request<InvoiceWithDetails>(`/api/invoices/${id}/send`, {
      method: 'POST',
    });
  }

  async updateInvoiceStatus(id: string, status: string): Promise<ApiResponse<InvoiceWithDetails>> {
    return this.request<InvoiceWithDetails>(`/api/invoices/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status }),
    });
  }

  // Health check
  async healthCheck(): Promise<any> {
    return this.request('/health');
  }
}

// Export singleton instance
export const apiClient = new ApiClient();
export default apiClient;
