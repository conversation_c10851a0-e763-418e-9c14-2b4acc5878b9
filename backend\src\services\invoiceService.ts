import prisma from '@/config/database';
import { 
  Invoice, 
  InvoiceWithDetails,
  CreateInvoiceRequest, 
  UpdateInvoiceRequest, 
  InvoiceFilters,
  ApiResponse 
} from '@/types/models';

export class InvoiceService {
  // إنشاء فاتورة جديدة
  static async createInvoice(
    tenantId: string, 
    userId: string,
    data: CreateInvoiceRequest
  ): Promise<ApiResponse<InvoiceWithDetails>> {
    try {
      // التحقق من وجود العميل
      const client = await prisma.client.findFirst({
        where: {
          id: data.clientId,
          tenantId,
          isActive: true,
        },
      });

      if (!client) {
        return {
          success: false,
          message: 'العميل غير موجود أو غير نشط',
          errors: ['CLIENT_NOT_FOUND'],
        };
      }

      // الحصول على إعدادات المؤسسة
      const tenantSettings = await prisma.tenantSettings.findUnique({
        where: { tenantId },
      });

      // إنشاء رقم الفاتورة
      const invoicePrefix = tenantSettings?.invoicePrefix || 'INV';
      const nextNumber = tenantSettings?.nextInvoiceNumber || 1;
      const invoiceNumber = `${invoicePrefix}-${new Date().getFullYear()}-${nextNumber.toString().padStart(3, '0')}`;

      // حساب المبالغ
      const subtotal = data.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
      const taxRate = data.taxRate || tenantSettings?.defaultTaxRate || 0;
      const discountRate = data.discountRate || 0;
      const taxAmount = subtotal * taxRate;
      const discountAmount = subtotal * discountRate;
      const totalAmount = subtotal + taxAmount - discountAmount;

      // إنشاء الفاتورة مع العناصر في معاملة واحدة
      const result = await prisma.$transaction(async (tx) => {
        // إنشاء الفاتورة
        const invoice = await tx.invoice.create({
          data: {
            tenantId,
            userId,
            clientId: data.clientId,
            invoiceNumber,
            status: 'DRAFT',
            issueDate: new Date(),
            dueDate: new Date(data.dueDate),
            subtotal,
            taxRate,
            taxAmount,
            discountRate,
            discountAmount,
            totalAmount,
            currency: data.currency || tenantSettings?.defaultCurrency || 'USD',
            notes: data.notes,
            terms: data.terms,
          },
        });

        // إنشاء عناصر الفاتورة
        const invoiceItems = await Promise.all(
          data.items.map((item, index) =>
            tx.invoiceItem.create({
              data: {
                invoiceId: invoice.id,
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.quantity * item.unitPrice,
                sortOrder: index + 1,
              },
            })
          )
        );

        // تحديث رقم الفاتورة التالي
        if (tenantSettings) {
          await tx.tenantSettings.update({
            where: { tenantId },
            data: { nextInvoiceNumber: nextNumber + 1 },
          });
        }

        return { invoice, invoiceItems };
      });

      // الحصول على الفاتورة مع التفاصيل
      const invoiceWithDetails = await this.getInvoiceWithDetails(tenantId, result.invoice.id);

      return {
        success: true,
        message: 'تم إنشاء الفاتورة بنجاح',
        data: invoiceWithDetails!,
      };
    } catch (error) {
      console.error('Error creating invoice:', error);
      return {
        success: false,
        message: 'فشل في إنشاء الفاتورة',
        errors: ['CREATE_INVOICE_FAILED'],
      };
    }
  }

  // الحصول على جميع الفواتير
  static async getInvoices(
    tenantId: string, 
    filters: InvoiceFilters = {}
  ): Promise<ApiResponse<InvoiceWithDetails[]>> {
    try {
      const { page = 1, limit = 10, status, clientId, startDate, endDate, search } = filters;
      const skip = (page - 1) * limit;

      // بناء شروط البحث
      const where: any = { tenantId };
      
      if (status) {
        where.status = status;
      }

      if (clientId) {
        where.clientId = clientId;
      }

      if (startDate || endDate) {
        where.issueDate = {};
        if (startDate) where.issueDate.gte = new Date(startDate);
        if (endDate) where.issueDate.lte = new Date(endDate);
      }

      if (search) {
        where.OR = [
          { invoiceNumber: { contains: search, mode: 'insensitive' } },
          { notes: { contains: search, mode: 'insensitive' } },
          { client: { name: { contains: search, mode: 'insensitive' } } },
        ];
      }

      // الحصول على الفواتير مع التفاصيل
      const [invoices, total] = await Promise.all([
        prisma.invoice.findMany({
          where,
          skip,
          take: limit,
          include: {
            client: true,
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            items: {
              orderBy: { sortOrder: 'asc' },
            },
            payments: {
              orderBy: { paymentDate: 'desc' },
            },
          },
          orderBy: { createdAt: 'desc' },
        }),
        prisma.invoice.count({ where }),
      ]);

      return {
        success: true,
        message: 'تم جلب الفواتير بنجاح',
        data: invoices as InvoiceWithDetails[],
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching invoices:', error);
      return {
        success: false,
        message: 'فشل في جلب الفواتير',
        errors: ['FETCH_INVOICES_FAILED'],
      };
    }
  }

  // الحصول على فاتورة واحدة
  static async getInvoiceById(
    tenantId: string, 
    invoiceId: string
  ): Promise<ApiResponse<InvoiceWithDetails>> {
    try {
      const invoice = await this.getInvoiceWithDetails(tenantId, invoiceId);

      if (!invoice) {
        return {
          success: false,
          message: 'الفاتورة غير موجودة',
          errors: ['INVOICE_NOT_FOUND'],
        };
      }

      return {
        success: true,
        message: 'تم جلب الفاتورة بنجاح',
        data: invoice,
      };
    } catch (error) {
      console.error('Error fetching invoice:', error);
      return {
        success: false,
        message: 'فشل في جلب الفاتورة',
        errors: ['FETCH_INVOICE_FAILED'],
      };
    }
  }

  // تحديث فاتورة
  static async updateInvoice(
    tenantId: string, 
    invoiceId: string, 
    data: UpdateInvoiceRequest
  ): Promise<ApiResponse<InvoiceWithDetails>> {
    try {
      // التحقق من وجود الفاتورة
      const existingInvoice = await prisma.invoice.findFirst({
        where: {
          id: invoiceId,
          tenantId,
        },
      });

      if (!existingInvoice) {
        return {
          success: false,
          message: 'الفاتورة غير موجودة',
          errors: ['INVOICE_NOT_FOUND'],
        };
      }

      // التحقق من إمكانية التعديل
      if (existingInvoice.status === 'PAID') {
        return {
          success: false,
          message: 'لا يمكن تعديل فاتورة مدفوعة',
          errors: ['CANNOT_EDIT_PAID_INVOICE'],
        };
      }

      // تحديث الفاتورة
      const result = await prisma.$transaction(async (tx) => {
        // حذف العناصر القديمة إذا تم تقديم عناصر جديدة
        if (data.items) {
          await tx.invoiceItem.deleteMany({
            where: { invoiceId },
          });

          // إنشاء العناصر الجديدة
          await Promise.all(
            data.items.map((item, index) =>
              tx.invoiceItem.create({
                data: {
                  invoiceId,
                  description: item.description,
                  quantity: item.quantity,
                  unitPrice: item.unitPrice,
                  totalPrice: item.quantity * item.unitPrice,
                  sortOrder: index + 1,
                },
              })
            )
          );

          // إعادة حساب المبالغ
          const subtotal = data.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
          const taxRate = data.taxRate || existingInvoice.taxRate;
          const discountRate = data.discountRate || existingInvoice.discountRate;
          const taxAmount = subtotal * taxRate;
          const discountAmount = subtotal * discountRate;
          const totalAmount = subtotal + taxAmount - discountAmount;

          data.subtotal = subtotal;
          data.taxAmount = taxAmount;
          data.discountAmount = discountAmount;
          data.totalAmount = totalAmount;
        }

        // تحديث الفاتورة
        const updatedInvoice = await tx.invoice.update({
          where: { id: invoiceId },
          data: {
            ...data,
            dueDate: data.dueDate ? new Date(data.dueDate) : undefined,
            sentAt: data.status === 'SENT' && existingInvoice.status === 'DRAFT' ? new Date() : undefined,
          },
        });

        return updatedInvoice;
      });

      // الحصول على الفاتورة المحدثة مع التفاصيل
      const invoiceWithDetails = await this.getInvoiceWithDetails(tenantId, invoiceId);

      return {
        success: true,
        message: 'تم تحديث الفاتورة بنجاح',
        data: invoiceWithDetails!,
      };
    } catch (error) {
      console.error('Error updating invoice:', error);
      return {
        success: false,
        message: 'فشل في تحديث الفاتورة',
        errors: ['UPDATE_INVOICE_FAILED'],
      };
    }
  }

  // حذف فاتورة
  static async deleteInvoice(
    tenantId: string, 
    invoiceId: string
  ): Promise<ApiResponse<void>> {
    try {
      // التحقق من وجود الفاتورة
      const existingInvoice = await prisma.invoice.findFirst({
        where: {
          id: invoiceId,
          tenantId,
        },
      });

      if (!existingInvoice) {
        return {
          success: false,
          message: 'الفاتورة غير موجودة',
          errors: ['INVOICE_NOT_FOUND'],
        };
      }

      // التحقق من إمكانية الحذف
      if (existingInvoice.status === 'PAID') {
        return {
          success: false,
          message: 'لا يمكن حذف فاتورة مدفوعة',
          errors: ['CANNOT_DELETE_PAID_INVOICE'],
        };
      }

      // حذف الفاتورة والعناصر المرتبطة
      await prisma.$transaction(async (tx) => {
        await tx.invoiceItem.deleteMany({
          where: { invoiceId },
        });

        await tx.invoice.delete({
          where: { id: invoiceId },
        });
      });

      return {
        success: true,
        message: 'تم حذف الفاتورة بنجاح',
      };
    } catch (error) {
      console.error('Error deleting invoice:', error);
      return {
        success: false,
        message: 'فشل في حذف الفاتورة',
        errors: ['DELETE_INVOICE_FAILED'],
      };
    }
  }

  // دالة مساعدة للحصول على الفاتورة مع التفاصيل
  private static async getInvoiceWithDetails(
    tenantId: string, 
    invoiceId: string
  ): Promise<InvoiceWithDetails | null> {
    return await prisma.invoice.findFirst({
      where: {
        id: invoiceId,
        tenantId,
      },
      include: {
        client: true,
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        items: {
          orderBy: { sortOrder: 'asc' },
        },
        payments: {
          orderBy: { paymentDate: 'desc' },
        },
      },
    }) as InvoiceWithDetails | null;
  }
}
