{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.1.11", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.8.4", "@types/js-cookie": "^3.0.6", "axios": "^1.6.2", "date-fns": "^2.30.0", "framer-motion": "^10.16.5", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jspdf": "^2.5.1", "next": "15.3.4", "react": "^18.2.0", "react-datepicker": "^4.24.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-select": "^5.8.0", "recharts": "^2.8.0", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^13.4.0", "@types/node": "^20", "@types/react": "^18", "@types/react-datepicker": "^4.19.4", "@types/react-dom": "^18", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "tailwindcss": "^4", "typescript": "^5"}}