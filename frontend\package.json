{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "next": "15.3.4", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "zustand": "^4.4.7", "axios": "^1.6.2", "@tanstack/react-query": "^5.8.4", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.5", "recharts": "^2.8.0", "date-fns": "^2.30.0", "react-datepicker": "^4.24.0", "react-select": "^5.8.0", "@stripe/stripe-js": "^2.1.11", "@stripe/react-stripe-js": "^2.4.0", "jspdf": "^2.5.1", "html2canvas": "^1.4.1"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.4", "@eslint/eslintrc": "^3", "@types/react-datepicker": "^4.19.4", "jest": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5", "jest-environment-jsdom": "^29.7.0"}}