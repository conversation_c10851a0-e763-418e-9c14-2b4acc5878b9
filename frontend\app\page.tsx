'use client';

import { useEffect, useState } from 'react';

export default function HomePage() {
  const [apiStatus, setApiStatus] = useState<string>('جاري التحقق...');
  const [apiData, setApiData] = useState<any>(null);

  useEffect(() => {
    // Test API connection
    fetch('http://localhost:5001/health')
      .then(res => res.json())
      .then(data => {
        setApiStatus('✅ متصل');
        setApiData(data);
      })
      .catch(err => {
        setApiStatus('❌ غير متصل');
        console.error('API Error:', err);
      });
  }, []);

  const testLogin = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'admin123'
        })
      });
      
      const data = await response.json();
      alert('نتيجة تسجيل الدخول: ' + JSON.stringify(data, null, 2));
    } catch (error) {
      alert('خطأ في تسجيل الدخول: ' + error);
    }
  };

  return (
    <div style={{ 
      fontFamily: 'Arial, sans-serif', 
      padding: '20px', 
      direction: 'rtl',
      backgroundColor: '#f5f5f5',
      minHeight: '100vh'
    }}>
      <div style={{
        maxWidth: '800px',
        margin: '0 auto',
        backgroundColor: 'white',
        padding: '30px',
        borderRadius: '10px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
      }}>
        <h1 style={{ 
          color: '#2563eb', 
          textAlign: 'center',
          marginBottom: '30px',
          fontSize: '2.5rem'
        }}>
          🚀 نظام إدارة الفواتير
        </h1>
        
        <div style={{
          backgroundColor: '#f8fafc',
          padding: '20px',
          borderRadius: '8px',
          marginBottom: '20px'
        }}>
          <h2 style={{ color: '#374151', marginBottom: '15px' }}>حالة النظام</h2>
          <p><strong>Backend API:</strong> {apiStatus}</p>
          <p><strong>Frontend:</strong> ✅ يعمل</p>
          <p><strong>المنفذ:</strong> http://localhost:3000</p>
          <p><strong>API المنفذ:</strong> http://localhost:5001</p>
        </div>

        {apiData && (
          <div style={{
            backgroundColor: '#ecfdf5',
            padding: '20px',
            borderRadius: '8px',
            marginBottom: '20px'
          }}>
            <h3 style={{ color: '#065f46', marginBottom: '10px' }}>معلومات API</h3>
            <pre style={{ 
              backgroundColor: 'white', 
              padding: '10px', 
              borderRadius: '4px',
              fontSize: '12px',
              overflow: 'auto'
            }}>
              {JSON.stringify(apiData, null, 2)}
            </pre>
          </div>
        )}

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '15px',
          marginBottom: '30px'
        }}>
          <button
            onClick={testLogin}
            style={{
              backgroundColor: '#2563eb',
              color: 'white',
              padding: '12px 20px',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            🔐 اختبار تسجيل الدخول
          </button>
          
          <button
            onClick={() => window.open('http://localhost:5001/api', '_blank')}
            style={{
              backgroundColor: '#059669',
              color: 'white',
              padding: '12px 20px',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            📡 فتح API
          </button>
          
          <button
            onClick={() => window.open('http://localhost/phpmyadmin', '_blank')}
            style={{
              backgroundColor: '#dc2626',
              color: 'white',
              padding: '12px 20px',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            🗄️ قاعدة البيانات
          </button>
          
          <button
            onClick={() => window.open('file:///D:/web_invoice/frontend/index.html', '_blank')}
            style={{
              backgroundColor: '#7c3aed',
              color: 'white',
              padding: '12px 20px',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            🧪 واجهة الاختبار
          </button>
        </div>

        <div style={{
          backgroundColor: '#fef3c7',
          padding: '20px',
          borderRadius: '8px',
          marginBottom: '20px'
        }}>
          <h3 style={{ color: '#92400e', marginBottom: '15px' }}>الحسابات التجريبية</h3>
          
          <div style={{ marginBottom: '15px' }}>
            <h4 style={{ color: '#374151', marginBottom: '5px' }}>مدير عام:</h4>
            <p style={{ margin: '0', fontSize: '14px' }}>
              البريد: <EMAIL><br/>
              كلمة المرور: admin123
            </p>
          </div>
          
          <div style={{ marginBottom: '15px' }}>
            <h4 style={{ color: '#374151', marginBottom: '5px' }}>مستخدم عادي:</h4>
            <p style={{ margin: '0', fontSize: '14px' }}>
              البريد: <EMAIL><br/>
              كلمة المرور: admin123
            </p>
          </div>
          
          <div>
            <h4 style={{ color: '#374151', marginBottom: '5px' }}>مدير:</h4>
            <p style={{ margin: '0', fontSize: '14px' }}>
              البريد: <EMAIL><br/>
              كلمة المرور: admin123
            </p>
          </div>
        </div>

        <div style={{
          backgroundColor: '#e0f2fe',
          padding: '20px',
          borderRadius: '8px'
        }}>
          <h3 style={{ color: '#0277bd', marginBottom: '15px' }}>الروابط المفيدة</h3>
          <ul style={{ margin: '0', paddingRight: '20px' }}>
            <li><a href="http://localhost:5001/health" target="_blank" style={{ color: '#0277bd' }}>صحة النظام</a></li>
            <li><a href="http://localhost:5001/api" target="_blank" style={{ color: '#0277bd' }}>معلومات API</a></li>
            <li><a href="http://localhost:5001/api/clients" target="_blank" style={{ color: '#0277bd' }}>العملاء</a></li>
            <li><a href="http://localhost:5001/api/invoices" target="_blank" style={{ color: '#0277bd' }}>الفواتير</a></li>
            <li><a href="http://localhost:5001/api/dashboard/stats" target="_blank" style={{ color: '#0277bd' }}>إحصائيات لوحة التحكم</a></li>
          </ul>
        </div>

        <div style={{
          textAlign: 'center',
          marginTop: '30px',
          padding: '20px',
          backgroundColor: '#f1f5f9',
          borderRadius: '8px'
        }}>
          <h3 style={{ color: '#475569', marginBottom: '10px' }}>حالة التطوير</h3>
          <p style={{ margin: '0', color: '#64748b' }}>
            ✅ Backend API يعمل<br/>
            ✅ قاعدة البيانات متصلة<br/>
            ✅ البيانات التجريبية متاحة<br/>
            🚧 Frontend قيد التطوير
          </p>
        </div>
      </div>
    </div>
  );
}
