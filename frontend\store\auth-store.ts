import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiClient, type User, type Tenant } from '../lib/api-client';

interface AuthState {
  // State
  user: User | null;
  tenant: Tenant | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  login: (email: string, password: string) => Promise<boolean>;
  register: (userData: any) => Promise<boolean>;
  logout: () => void;
  getProfile: () => Promise<void>;
  setUser: (user: User | null) => void;
  setTenant: (tenant: Tenant | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  user: null,
  tenant: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Login action
      login: async (email: string, password: string) => {
        try {
          set({ isLoading: true, error: null });

          const response = await apiClient.login(email, password);

          if (response.success && response.data) {
            set({
              user: response.data.user,
              tenant: response.data.tenant,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
            return true;
          } else {
            set({
              error: response.message || 'فشل في تسجيل الدخول',
              isLoading: false,
            });
            return false;
          }
        } catch (error: any) {
          set({
            error: error.message || 'حدث خطأ أثناء تسجيل الدخول',
            isLoading: false,
          });
          return false;
        }
      },

      // Register action
      register: async (userData: any) => {
        try {
          set({ isLoading: true, error: null });

          const response = await apiClient.register(userData);

          if (response.success) {
            set({
              isLoading: false,
              error: null,
            });
            return true;
          } else {
            set({
              error: response.message || 'فشل في إنشاء الحساب',
              isLoading: false,
            });
            return false;
          }
        } catch (error: any) {
          set({
            error: error.message || 'حدث خطأ أثناء إنشاء الحساب',
            isLoading: false,
          });
          return false;
        }
      },

      // Logout action
      logout: () => {
        apiClient.logout();
        set({ ...initialState });
      },

      // Get user profile
      getProfile: async () => {
        try {
          if (!apiClient.isAuthenticated()) {
            set({ ...initialState });
            return;
          }

          set({ isLoading: true, error: null });

          const response = await apiClient.getProfile();

          if (response.success && response.data) {
            set({
              user: response.data,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
          } else {
            set({
              ...initialState,
              error: response.message || 'فشل في جلب بيانات المستخدم',
            });
          }
        } catch (error: any) {
          set({
            ...initialState,
            error: error.message || 'حدث خطأ أثناء جلب بيانات المستخدم',
          });
        }
      },

      // Set user
      setUser: (user: User | null) => {
        set({ user, isAuthenticated: !!user });
      },

      // Set tenant
      setTenant: (tenant: Tenant | null) => {
        set({ tenant });
      },

      // Set loading
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      // Set error
      setError: (error: string | null) => {
        set({ error });
      },

      // Clear error
      clearError: () => {
        set({ error: null });
      },

      // Reset state
      reset: () => {
        set({ ...initialState });
      },
    }),
    {
      name: 'auth-store',
      partialize: (state) => ({
        user: state.user,
        tenant: state.tenant,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Selectors
export const useAuth = () => {
  const store = useAuthStore();
  return {
    user: store.user,
    tenant: store.tenant,
    isAuthenticated: store.isAuthenticated,
    isLoading: store.isLoading,
    error: store.error,
  };
};

export const useAuthActions = () => {
  const store = useAuthStore();
  return {
    login: store.login,
    register: store.register,
    logout: store.logout,
    getProfile: store.getProfile,
    setUser: store.setUser,
    setTenant: store.setTenant,
    setLoading: store.setLoading,
    setError: store.setError,
    clearError: store.clearError,
    reset: store.reset,
  };
};
