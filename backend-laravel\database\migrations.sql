-- Invoice Management System Database Schema

-- Create Database
CREATE DATABASE IF NOT EXISTS invoice_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE invoice_management;

-- Tenants Table (Multi-tenant support)
CREATE TABLE tenants (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(100) UNIQUE NOT NULL,
    settings JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Users Table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('admin', 'user') DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    email_verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    INDEX idx_tenant_email (tenant_id, email)
);

-- Clients Table
CREATE TABLE clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100) DEFAULT 'المملكة العربية السعودية',
    tax_number VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    INDEX idx_tenant_name (tenant_id, name),
    INDEX idx_tenant_email (tenant_id, email)
);

-- Invoices Table
CREATE TABLE invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT NOT NULL,
    client_id INT NOT NULL,
    invoice_number VARCHAR(50) NOT NULL,
    issue_date DATE NOT NULL,
    due_date DATE NOT NULL,
    status ENUM('DRAFT', 'SENT', 'PAID', 'OVERDUE', 'CANCELLED') DEFAULT 'DRAFT',
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    tax_rate DECIMAL(5,2) NOT NULL DEFAULT 15.00,
    tax_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'SAR',
    notes TEXT,
    terms TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    UNIQUE KEY unique_invoice_number (tenant_id, invoice_number),
    INDEX idx_tenant_status (tenant_id, status),
    INDEX idx_tenant_client (tenant_id, client_id),
    INDEX idx_due_date (due_date)
);

-- Invoice Items Table
CREATE TABLE invoice_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    description TEXT NOT NULL,
    quantity DECIMAL(10,2) NOT NULL DEFAULT 1.00,
    unit_price DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    total_price DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
);

-- Payments Table
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    payment_date DATE NOT NULL,
    method ENUM('cash', 'bank_transfer', 'credit_card', 'check', 'other') DEFAULT 'bank_transfer',
    reference_number VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    INDEX idx_invoice_date (invoice_id, payment_date)
);

-- Insert Sample Data

-- Sample Tenant
INSERT INTO tenants (name, domain, is_active) VALUES 
('شركة الأمل للتجارة', 'alamal', TRUE);

-- Sample User
INSERT INTO users (tenant_id, first_name, last_name, email, phone, password_hash, role) VALUES 
(1, 'أحمد', 'محمد', '<EMAIL>', '+************', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');
-- Password is: admin123

-- Sample Clients
INSERT INTO clients (tenant_id, name, email, phone, address, city, country, tax_number, is_active) VALUES 
(1, 'شركة الأمل للتجارة', '<EMAIL>', '+************', 'شارع الملك فهد، الرياض', 'الرياض', 'المملكة العربية السعودية', '*********', TRUE),
(1, 'مؤسسة النور', '<EMAIL>', '+966507654321', 'طريق الملك عبدالعزيز، جدة', 'جدة', 'المملكة العربية السعودية', '*********', TRUE),
(1, 'شركة الفجر للمقاولات', '<EMAIL>', '+966512345678', 'شارع الأمير سلطان، الدمام', 'الدمام', 'المملكة العربية السعودية', '*********', TRUE),
(1, 'مكتب الإبداع للاستشارات', '<EMAIL>', '+966523456789', 'حي الملقا، الرياض', 'الرياض', 'المملكة العربية السعودية', '*********', TRUE),
(1, 'شركة التقنية المتقدمة', '<EMAIL>', '+966534567890', 'شارع التحلية، جدة', 'جدة', 'المملكة العربية السعودية', '*********', TRUE);

-- Sample Invoices
INSERT INTO invoices (tenant_id, client_id, invoice_number, issue_date, due_date, status, subtotal, tax_rate, tax_amount, total_amount, currency, notes) VALUES 
(1, 1, 'INV-2024-001', '2024-01-15', '2024-02-15', 'SENT', 10000.00, 15.00, 1500.00, 11500.00, 'SAR', 'فاتورة خدمات استشارية'),
(1, 2, 'INV-2024-002', '2024-01-20', '2024-02-20', 'PAID', 8000.00, 15.00, 1200.00, 9200.00, 'SAR', 'تطوير موقع إلكتروني'),
(1, 3, 'INV-2024-003', '2024-01-25', '2024-02-25', 'DRAFT', 15000.00, 15.00, 2250.00, 17250.00, 'SAR', 'أعمال مقاولات'),
(1, 4, 'INV-2024-004', '2024-02-01', '2024-03-01', 'SENT', 5000.00, 15.00, 750.00, 5750.00, 'SAR', 'استشارات إدارية'),
(1, 5, 'INV-2024-005', '2024-02-05', '2024-01-05', 'OVERDUE', 12000.00, 15.00, 1800.00, 13800.00, 'SAR', 'حلول تقنية');

-- Sample Invoice Items
INSERT INTO invoice_items (invoice_id, description, quantity, unit_price, total_price) VALUES 
(1, 'استشارة تقنية', 10.00, 1000.00, 10000.00),
(2, 'تطوير موقع إلكتروني', 1.00, 8000.00, 8000.00),
(3, 'أعمال بناء وتشطيب', 1.00, 15000.00, 15000.00),
(4, 'استشارة إدارية', 5.00, 1000.00, 5000.00),
(5, 'تطوير نظام إدارة', 1.00, 12000.00, 12000.00);

-- Sample Payments
INSERT INTO payments (invoice_id, amount, payment_date, method, reference_number, notes) VALUES 
(2, 9200.00, '2024-01-25', 'bank_transfer', 'TXN-001', 'تم الدفع بالتحويل البنكي');

-- Create Views for easier data access

-- Clients with invoice statistics
CREATE VIEW clients_with_stats AS
SELECT 
    c.*,
    COUNT(i.id) as total_invoices,
    COALESCE(SUM(CASE WHEN i.status = 'PAID' THEN i.total_amount ELSE 0 END), 0) as paid_amount,
    COALESCE(SUM(CASE WHEN i.status IN ('SENT', 'OVERDUE') THEN i.total_amount ELSE 0 END), 0) as pending_amount,
    COALESCE(SUM(i.total_amount), 0) as total_amount
FROM clients c
LEFT JOIN invoices i ON c.id = i.client_id
GROUP BY c.id;

-- Invoices with client details
CREATE VIEW invoices_with_details AS
SELECT 
    i.*,
    c.name as client_name,
    c.email as client_email,
    c.phone as client_phone,
    (SELECT COUNT(*) FROM invoice_items ii WHERE ii.invoice_id = i.id) as items_count,
    (SELECT COALESCE(SUM(p.amount), 0) FROM payments p WHERE p.invoice_id = i.id) as paid_amount
FROM invoices i
LEFT JOIN clients c ON i.client_id = c.id;
