import { Router } from 'express';
import { AuthController } from '../controllers/authController';
import { authenticate, authRateLimit, optionalAuth } from '../middleware/auth';

const router = Router();

// Public routes (no authentication required)

// Register new user and tenant
router.post('/register', 
  authRateLimit(3, 15 * 60 * 1000), // 3 attempts per 15 minutes
  AuthController.register
);

// Login user
router.post('/login', 
  authRateLimit(5, 15 * 60 * 1000), // 5 attempts per 15 minutes
  AuthController.login
);

// Refresh access token
router.post('/refresh', 
  authRateLimit(10, 15 * 60 * 1000), // 10 attempts per 15 minutes
  AuthController.refreshToken
);

// Request password reset
router.post('/forgot-password', 
  authRateLimit(3, 15 * 60 * 1000), // 3 attempts per 15 minutes
  AuthController.requestPasswordReset
);

// Verify email
router.get('/verify-email/:token', 
  AuthController.verifyEmail
);

// Check password strength (utility endpoint)
router.post('/check-password-strength', 
  authRateLimit(20, 15 * 60 * 1000), // 20 attempts per 15 minutes
  AuthController.checkPasswordStrength
);

// Protected routes (authentication required)

// Get current user profile
router.get('/profile', 
  authenticate,
  AuthController.getProfile
);

// Update user profile
router.put('/profile', 
  authenticate,
  AuthController.updateProfile
);

// Change password
router.post('/change-password', 
  authenticate,
  authRateLimit(5, 15 * 60 * 1000), // 5 attempts per 15 minutes
  AuthController.changePassword
);

// Logout (client-side token removal)
router.post('/logout', 
  optionalAuth, // Optional auth to log the event
  AuthController.logout
);

export default router;
