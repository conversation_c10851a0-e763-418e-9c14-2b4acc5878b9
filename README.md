# نظام إدارة الفواتير - Web Invoice SaaS

نظام شامل لإدارة الفواتير والعملاء مع دعم Multi-Tenant وواجهة عربية حديثة.

## 🌟 الميزات الرئيسية

### 💼 إدارة العملاء
- عرض وبحث وفلترة العملاء
- إحصائيات مفصلة لكل عميل
- تتبع تاريخ التعاملات
- واجهة سهلة الاستخدام

### 📄 إدارة الفواتير
- عرض جميع الفواتير مع التفاصيل
- حالات متعددة (مسودة، مرسلة، مدفوعة، متأخرة)
- بحث وفلترة متقدمة
- تتبع المدفوعات والمتأخرات

### 📊 لوحة التحكم التفاعلية
- إحصائيات شاملة ومرئية
- مخططات المبيعات الشهرية
- تتبع الفواتير المتأخرة
- أفضل العملاء والأداء

### 🏢 Multi-Tenant
- عزل كامل للبيانات بين المؤسسات
- إعدادات مخصصة لكل مؤسسة
- إدارة المستخدمين والصلاحيات
- نطاقات منفصلة

### 🔐 الأمان
- مصادقة JWT آمنة
- تشفير كلمات المرور
- صلاحيات مفصلة حسب الأدوار
- Rate limiting للحماية

## 🛠️ التقنيات المستخدمة

### Frontend
- **Next.js 14** - إطار React متقدم
- **TypeScript** - لغة البرمجة
- **Tailwind CSS** - التصميم المتجاوب
- **React Hook Form** - إدارة النماذج
- **Zustand** - إدارة الحالة
- **React Query** - إدارة البيانات
- **Recharts** - المخططات البيانية

### Backend
- **Node.js** + **TypeScript**
- **Express.js** - إطار العمل
- **Prisma** - ORM لقاعدة البيانات
- **MySQL** - قاعدة البيانات
- **JWT** - المصادقة والتفويض
- **bcrypt** - تشفير كلمات المرور

### قاعدة البيانات
- **MySQL 8.0+**
- **XAMPP** للتطوير المحلي
- دعم كامل للغة العربية (UTF-8)

## 🚀 التثبيت والإعداد

### المتطلبات الأساسية
- Node.js 18+ 
- XAMPP (MySQL + Apache)
- Git

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd web_invoice
```

### 2. التشغيل السريع
```bash
# تشغيل النظام كاملاً
start-complete-system.bat
```

### 3. الإعداد اليدوي (إذا لزم الأمر)
```bash
# إعداد قاعدة البيانات
scripts\setup-complete-xampp.bat

# إعداد Backend
cd backend
npm install
copy .env.example .env
# حدث DATABASE_URL في .env
npx prisma generate
npx prisma db push
npm run db:seed

# إعداد Frontend
cd ../frontend
npm install

# تشغيل النظام
npm run dev  # في مجلد frontend
npm run dev  # في مجلد backend (نافذة منفصلة)
```

## 🔗 الروابط المهمة

### التطوير المحلي
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **API Health**: http://localhost:5000/health
- **API Info**: http://localhost:5000/api
- **phpMyAdmin**: http://localhost/phpmyadmin

### صفحات النظام
- **تسجيل الدخول**: http://localhost:3000/auth/login
- **لوحة التحكم**: http://localhost:3000/dashboard
- **العملاء**: http://localhost:3000/dashboard/clients
- **الفواتير**: http://localhost:3000/dashboard/invoices
- **التقارير**: http://localhost:3000/dashboard/reports
- **الإعدادات**: http://localhost:3000/dashboard/settings

## 👤 الحسابات التجريبية

### مدير عام
```
البريد الإلكتروني: <EMAIL>
كلمة المرور: admin123
الدور: SUPER_ADMIN
المؤسسة: شركة التقنية المتقدمة
```

### مستخدم عادي
```
البريد الإلكتروني: <EMAIL>
كلمة المرور: admin123
الدور: USER
المؤسسة: شركة التقنية المتقدمة
```

### مدير
```
البريد الإلكتروني: <EMAIL>
كلمة المرور: admin123
الدور: ADMIN
المؤسسة: مؤسسة الابتكار الرقمي
```

## 📊 البيانات التجريبية

### المؤسسات
- شركة التقنية المتقدمة
- مؤسسة الابتكار الرقمي

### العملاء
- شركة البناء الحديث
- مؤسسة التجارة الإلكترونية
- شركة الخدمات المالية

### الفواتير
- **INV-2024-001** (مرسلة - 11,500 ريال)
- **INV-2024-002** (مدفوعة - 8,250 ريال)
- **DIG-2024-001** (مسودة - 17,250 ريال)

## ✅ الميزات المكتملة

- [x] **واجهة المستخدم الكاملة** - Next.js مع TypeScript و Tailwind CSS
- [x] **نظام المصادقة والتفويض** - تسجيل دخول وإنشاء حسابات
- [x] **لوحة التحكم التفاعلية** - إحصائيات شاملة ومرئية
- [x] **إدارة العملاء** - عرض، بحث، وفلترة العملاء
- [x] **إدارة الفواتير** - عرض، بحث، وفلترة الفواتير
- [x] **التقارير والمخططات** - تحليل المبيعات والأداء
- [x] **الإعدادات المتقدمة** - تخصيص النظام والشركة
- [x] **دعم Multi-Tenant** - عزل كامل للبيانات
- [x] **واجهة عربية متجاوبة** - تصميم حديث ومتجاوب
- [x] **قاعدة بيانات MySQL** - مع بيانات تجريبية شاملة

## 🚧 الميزات القادمة

- [ ] إنشاء وتعديل العملاء والفواتير
- [ ] إرسال الفواتير بالبريد الإلكتروني
- [ ] تصدير PDF للفواتير
- [ ] تكامل مع بوابات الدفع (Stripe/PayPal)
- [ ] تطبيق الهاتف المحمول
- [ ] نظام الإشعارات المتقدم
- [ ] API للتكامل الخارجي

## 🧪 الاختبار

### اختبار سريع
```bash
# اختبار صحة النظام
curl http://localhost:5000/health

# اختبار تسجيل الدخول
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'
```

## 📁 هيكل المشروع

```
web_invoice/
├── frontend/                # Next.js Frontend
│   ├── app/                # App Router Pages
│   ├── components/         # React Components
│   ├── lib/               # API Client & Utils
│   ├── store/             # Zustand Store
│   ├── types/             # TypeScript Types
│   └── package.json
├── backend/               # Node.js Backend
│   ├── src/              # Source Code
│   ├── prisma/           # Database Schema
│   ├── server-basic.js   # Simple Server
│   └── package.json
├── database/             # Database Files
├── docs/                 # Documentation
├── scripts/              # Setup Scripts
└── README.md
```

## 🔧 أوامر مفيدة

### تشغيل النظام
```bash
# تشغيل كامل
start-complete-system.bat

# تشغيل Frontend فقط
cd frontend && npm run dev

# تشغيل Backend فقط
cd backend && npm run dev

# تشغيل Backend البسيط
cd backend && node server-basic.js
```

### قاعدة البيانات
```bash
# إعداد قاعدة البيانات
scripts\setup-complete-xampp.bat

# اختبار الاتصال
node scripts\test-xampp-connection.js

# Prisma Studio
cd backend && npx prisma studio
```

## 📚 الوثائق

- [دليل البدء السريع](QUICK-START.md)
- [دليل إعداد XAMPP](docs/XAMPP-SETUP.md)
- [دليل إعداد MySQL](docs/mysql-setup-guide.md)
- [وثائق API](docs/api-documentation.md)
- [مخطط قاعدة البيانات](docs/database-schema.md)

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch للميزة الجديدة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

للحصول على المساعدة:

1. راجع [الوثائق](docs/)
2. تحقق من [دليل البدء السريع](QUICK-START.md)
3. أنشئ Issue جديد
4. تواصل مع فريق التطوير

---

**🚀 النظام جاهز للاستخدام! تم تطوير هذا النظام بـ ❤️ لخدمة المجتمع العربي**
