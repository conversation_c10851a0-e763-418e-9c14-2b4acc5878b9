'use client';

import { Client } from '../../../lib/api-client';

interface ClientViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  client: Client;
}

export default function ClientViewModal({ isOpen, onClose, client }: ClientViewModalProps) {
  if (!isOpen) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div style={{
      position: 'fixed',
      inset: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 50,
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: 'rtl'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
        width: '90%',
        maxWidth: '600px',
        maxHeight: '90vh',
        overflow: 'auto'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '20px 24px',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{
              width: '48px',
              height: '48px',
              backgroundColor: '#dbeafe',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginLeft: '16px'
            }}>
              <span style={{
                fontSize: '20px',
                fontWeight: '600',
                color: '#2563eb'
              }}>
                {client.name.charAt(0)}
              </span>
            </div>
            <div>
              <h2 style={{
                fontSize: '20px',
                fontWeight: '600',
                color: '#111827',
                margin: '0 0 4px 0'
              }}>
                {client.name}
              </h2>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                <span style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  padding: '2px 8px',
                  borderRadius: '12px',
                  fontSize: '12px',
                  fontWeight: '500',
                  backgroundColor: client.isActive ? '#dcfce7' : '#f3f4f6',
                  color: client.isActive ? '#166534' : '#6b7280'
                }}>
                  {client.isActive ? 'نشط' : 'غير نشط'}
                </span>
              </div>
            </div>
          </div>
          <button
            onClick={onClose}
            style={{
              padding: '8px',
              color: '#6b7280',
              background: 'none',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '20px'
            }}
            onMouseOver={(e) => e.target.style.backgroundColor = '#f3f4f6'}
            onMouseOut={(e) => e.target.style.backgroundColor = 'transparent'}
          >
            ✕
          </button>
        </div>

        {/* Content */}
        <div style={{ padding: '24px' }}>
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr',
            gap: '24px'
          }} className="sm:grid-cols-2">
            
            {/* Contact Information */}
            <div>
              <h3 style={{
                fontSize: '16px',
                fontWeight: '600',
                color: '#111827',
                marginBottom: '16px'
              }}>
                معلومات الاتصال
              </h3>
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '12px'
              }}>
                {client.email && (
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px'
                  }}>
                    <div style={{
                      width: '32px',
                      height: '32px',
                      backgroundColor: '#f3f4f6',
                      borderRadius: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <span style={{ fontSize: '16px' }}>📧</span>
                    </div>
                    <div>
                      <p style={{
                        fontSize: '12px',
                        color: '#6b7280',
                        margin: '0 0 2px 0'
                      }}>
                        البريد الإلكتروني
                      </p>
                      <a
                        href={`mailto:${client.email}`}
                        style={{
                          fontSize: '14px',
                          color: '#2563eb',
                          textDecoration: 'none',
                          fontWeight: '500'
                        }}
                        onMouseOver={(e) => e.target.style.textDecoration = 'underline'}
                        onMouseOut={(e) => e.target.style.textDecoration = 'none'}
                      >
                        {client.email}
                      </a>
                    </div>
                  </div>
                )}

                {client.phone && (
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px'
                  }}>
                    <div style={{
                      width: '32px',
                      height: '32px',
                      backgroundColor: '#f3f4f6',
                      borderRadius: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <span style={{ fontSize: '16px' }}>📞</span>
                    </div>
                    <div>
                      <p style={{
                        fontSize: '12px',
                        color: '#6b7280',
                        margin: '0 0 2px 0'
                      }}>
                        رقم الهاتف
                      </p>
                      <a
                        href={`tel:${client.phone}`}
                        style={{
                          fontSize: '14px',
                          color: '#2563eb',
                          textDecoration: 'none',
                          fontWeight: '500'
                        }}
                        onMouseOver={(e) => e.target.style.textDecoration = 'underline'}
                        onMouseOut={(e) => e.target.style.textDecoration = 'none'}
                      >
                        {client.phone}
                      </a>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Business Information */}
            <div>
              <h3 style={{
                fontSize: '16px',
                fontWeight: '600',
                color: '#111827',
                marginBottom: '16px'
              }}>
                المعلومات التجارية
              </h3>
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '12px'
              }}>
                {client.taxNumber && (
                  <div>
                    <p style={{
                      fontSize: '12px',
                      color: '#6b7280',
                      margin: '0 0 4px 0'
                    }}>
                      الرقم الضريبي
                    </p>
                    <p style={{
                      fontSize: '14px',
                      color: '#111827',
                      fontWeight: '500',
                      margin: 0
                    }}>
                      {client.taxNumber}
                    </p>
                  </div>
                )}

                {client.city && (
                  <div>
                    <p style={{
                      fontSize: '12px',
                      color: '#6b7280',
                      margin: '0 0 4px 0'
                    }}>
                      المدينة
                    </p>
                    <p style={{
                      fontSize: '14px',
                      color: '#111827',
                      fontWeight: '500',
                      margin: 0
                    }}>
                      {client.city}
                    </p>
                  </div>
                )}

                {client.country && (
                  <div>
                    <p style={{
                      fontSize: '12px',
                      color: '#6b7280',
                      margin: '0 0 4px 0'
                    }}>
                      البلد
                    </p>
                    <p style={{
                      fontSize: '14px',
                      color: '#111827',
                      fontWeight: '500',
                      margin: 0
                    }}>
                      {client.country}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Address */}
            {client.address && (
              <div className="sm:col-span-2">
                <h3 style={{
                  fontSize: '16px',
                  fontWeight: '600',
                  color: '#111827',
                  marginBottom: '16px'
                }}>
                  العنوان
                </h3>
                <div style={{
                  padding: '16px',
                  backgroundColor: '#f9fafb',
                  borderRadius: '8px',
                  border: '1px solid #e5e7eb'
                }}>
                  <p style={{
                    fontSize: '14px',
                    color: '#111827',
                    lineHeight: '1.6',
                    margin: 0
                  }}>
                    {client.address}
                  </p>
                </div>
              </div>
            )}

            {/* Dates */}
            <div className="sm:col-span-2">
              <h3 style={{
                fontSize: '16px',
                fontWeight: '600',
                color: '#111827',
                marginBottom: '16px'
              }}>
                معلومات إضافية
              </h3>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: '16px'
              }}>
                <div>
                  <p style={{
                    fontSize: '12px',
                    color: '#6b7280',
                    margin: '0 0 4px 0'
                  }}>
                    تاريخ الإضافة
                  </p>
                  <p style={{
                    fontSize: '14px',
                    color: '#111827',
                    fontWeight: '500',
                    margin: 0
                  }}>
                    {formatDate(client.createdAt)}
                  </p>
                </div>
                <div>
                  <p style={{
                    fontSize: '12px',
                    color: '#6b7280',
                    margin: '0 0 4px 0'
                  }}>
                    آخر تحديث
                  </p>
                  <p style={{
                    fontSize: '14px',
                    color: '#111827',
                    fontWeight: '500',
                    margin: 0
                  }}>
                    {formatDate(client.updatedAt)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div style={{
          display: 'flex',
          justifyContent: 'flex-end',
          padding: '20px 24px',
          borderTop: '1px solid #e5e7eb',
          backgroundColor: '#f9fafb'
        }}>
          <button
            onClick={onClose}
            style={{
              padding: '12px 24px',
              backgroundColor: '#2563eb',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer'
            }}
            onMouseOver={(e) => e.target.style.backgroundColor = '#1d4ed8'}
            onMouseOut={(e) => e.target.style.backgroundColor = '#2563eb'}
          >
            إغلاق
          </button>
        </div>
      </div>
    </div>
  );
}
