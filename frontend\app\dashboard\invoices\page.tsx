'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  DocumentTextIcon,
  PaperAirplaneIcon,
  PrinterIcon,
} from '@heroicons/react/24/outline';
import { invoicesApi } from '@/lib/api';
import type { InvoiceWithDetails, InvoiceFilters } from '@/types';

// Invoice Status Badge Component
const InvoiceStatusBadge = ({ status }: { status: string }) => {
  const statusMap = {
    DRAFT: { label: 'مسودة', class: 'badge-gray' },
    SENT: { label: 'مرسلة', class: 'badge-blue' },
    PAID: { label: 'مدفوعة', class: 'badge-success' },
    OVERDUE: { label: 'متأخرة', class: 'badge-danger' },
    CANCELLED: { label: 'ملغية', class: 'badge-gray' },
  };
  
  const statusInfo = statusMap[status as keyof typeof statusMap] || statusMap.DRAFT;
  return (
    <span className={`badge ${statusInfo.class}`}>
      {statusInfo.label}
    </span>
  );
};

// Invoices Table Component
interface InvoicesTableProps {
  invoices: InvoiceWithDetails[];
  loading: boolean;
  onView: (invoice: InvoiceWithDetails) => void;
  onEdit: (invoice: InvoiceWithDetails) => void;
  onDelete: (invoice: InvoiceWithDetails) => void;
  onSend: (invoice: InvoiceWithDetails) => void;
  onPrint: (invoice: InvoiceWithDetails) => void;
}

const InvoicesTable = ({ 
  invoices, 
  loading, 
  onView, 
  onEdit, 
  onDelete, 
  onSend, 
  onPrint 
}: InvoicesTableProps) => {
  if (loading) {
    return (
      <div className="card">
        <div className="card-body">
          <div className="flex items-center justify-center py-12">
            <div className="loading-spinner w-8 h-8 me-3"></div>
            <span className="text-gray-600">جاري تحميل الفواتير...</span>
          </div>
        </div>
      </div>
    );
  }

  if (invoices.length === 0) {
    return (
      <div className="card">
        <div className="card-body">
          <div className="text-center py-12">
            <DocumentTextIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              لا توجد فواتير
            </h3>
            <p className="text-gray-500 mb-6">
              ابدأ بإنشاء فاتورتك الأولى
            </p>
            <button className="btn btn-primary">
              <PlusIcon className="w-4 h-4 me-2" />
              إنشاء فاتورة جديدة
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="card-body p-0">
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="table-header-cell">رقم الفاتورة</th>
                <th className="table-header-cell">العميل</th>
                <th className="table-header-cell">المبلغ</th>
                <th className="table-header-cell">تاريخ الإصدار</th>
                <th className="table-header-cell">تاريخ الاستحقاق</th>
                <th className="table-header-cell">الحالة</th>
                <th className="table-header-cell">الإجراءات</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {invoices.map((invoice) => {
                const isOverdue = invoice.status === 'SENT' && 
                  new Date(invoice.dueDate) < new Date();
                
                return (
                  <tr key={invoice.id} className="table-row">
                    <td className="table-cell">
                      <div className="font-medium text-gray-900">
                        {invoice.invoiceNumber}
                      </div>
                      <div className="text-sm text-gray-500">
                        {invoice.items.length} عنصر
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center me-3">
                          <span className="text-sm font-medium text-blue-600">
                            {invoice.client.name.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">
                            {invoice.client.name}
                          </div>
                          {invoice.client.email && (
                            <div className="text-sm text-gray-500">
                              {invoice.client.email}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="font-medium text-gray-900">
                        {invoice.totalAmount.toLocaleString('ar-SA')} {invoice.currency}
                      </div>
                      {invoice.payments.length > 0 && (
                        <div className="text-sm text-green-600">
                          مدفوع: {invoice.payments.reduce((sum, p) => sum + p.amount, 0).toLocaleString('ar-SA')}
                        </div>
                      )}
                    </td>
                    <td className="table-cell text-gray-500">
                      {new Date(invoice.issueDate).toLocaleDateString('ar-SA')}
                    </td>
                    <td className="table-cell">
                      <div className={`text-sm ${isOverdue ? 'text-red-600 font-medium' : 'text-gray-500'}`}>
                        {new Date(invoice.dueDate).toLocaleDateString('ar-SA')}
                        {isOverdue && (
                          <div className="text-xs">
                            متأخر {Math.ceil((Date.now() - new Date(invoice.dueDate).getTime()) / (1000 * 60 * 60 * 24))} يوم
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="table-cell">
                      <InvoiceStatusBadge status={isOverdue ? 'OVERDUE' : invoice.status} />
                    </td>
                    <td className="table-cell">
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <button
                          onClick={() => onView(invoice)}
                          className="p-1 text-gray-400 hover:text-blue-600"
                          title="عرض التفاصيل"
                        >
                          <EyeIcon className="w-4 h-4" />
                        </button>
                        
                        {invoice.status === 'DRAFT' && (
                          <button
                            onClick={() => onEdit(invoice)}
                            className="p-1 text-gray-400 hover:text-green-600"
                            title="تعديل"
                          >
                            <PencilIcon className="w-4 h-4" />
                          </button>
                        )}
                        
                        {(invoice.status === 'DRAFT' || invoice.status === 'SENT') && (
                          <button
                            onClick={() => onSend(invoice)}
                            className="p-1 text-gray-400 hover:text-blue-600"
                            title="إرسال"
                          >
                            <PaperAirplaneIcon className="w-4 h-4" />
                          </button>
                        )}
                        
                        <button
                          onClick={() => onPrint(invoice)}
                          className="p-1 text-gray-400 hover:text-purple-600"
                          title="طباعة"
                        >
                          <PrinterIcon className="w-4 h-4" />
                        </button>
                        
                        {invoice.status === 'DRAFT' && (
                          <button
                            onClick={() => onDelete(invoice)}
                            className="p-1 text-gray-400 hover:text-red-600"
                            title="حذف"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

// Search and Filters Component
interface SearchFiltersProps {
  filters: InvoiceFilters;
  onFiltersChange: (filters: InvoiceFilters) => void;
  onAddInvoice: () => void;
}

const SearchFilters = ({ filters, onFiltersChange, onAddInvoice }: SearchFiltersProps) => {
  const handleSearchChange = (search: string) => {
    onFiltersChange({ ...filters, search, page: 1 });
  };

  const handleStatusChange = (status: string) => {
    onFiltersChange({ 
      ...filters, 
      status: status === 'all' ? undefined : status as any, 
      page: 1 
    });
  };

  return (
    <div className="card">
      <div className="card-body">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          {/* Search */}
          <div className="flex-1 max-w-md">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="البحث في الفواتير..."
                className="form-input pr-10"
                value={filters.search || ''}
                onChange={(e) => handleSearchChange(e.target.value)}
              />
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
            <select
              className="form-input"
              value={filters.status || 'all'}
              onChange={(e) => handleStatusChange(e.target.value)}
            >
              <option value="all">جميع الفواتير</option>
              <option value="DRAFT">المسودات</option>
              <option value="SENT">المرسلة</option>
              <option value="PAID">المدفوعة</option>
              <option value="OVERDUE">المتأخرة</option>
              <option value="CANCELLED">الملغية</option>
            </select>

            <button
              onClick={onAddInvoice}
              className="btn btn-primary"
            >
              <PlusIcon className="w-4 h-4 me-2" />
              إنشاء فاتورة
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function InvoicesPage() {
  const [filters, setFilters] = useState<InvoiceFilters>({
    page: 1,
    limit: 10,
    search: '',
    status: undefined,
  });

  // Fetch invoices
  const {
    data: invoicesResponse,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['invoices', filters],
    queryFn: () => invoicesApi.getInvoices(filters),
  });

  const invoices = invoicesResponse?.data || [];
  const pagination = invoicesResponse?.pagination;

  const handleView = (invoice: InvoiceWithDetails) => {
    // TODO: Open invoice details modal or navigate to details page
    console.log('View invoice:', invoice);
  };

  const handleEdit = (invoice: InvoiceWithDetails) => {
    // TODO: Open edit invoice modal or navigate to edit page
    console.log('Edit invoice:', invoice);
  };

  const handleDelete = async (invoice: InvoiceWithDetails) => {
    if (confirm(`هل أنت متأكد من حذف الفاتورة "${invoice.invoiceNumber}"؟`)) {
      try {
        await invoicesApi.deleteInvoice(invoice.id);
        refetch();
      } catch (error) {
        console.error('Error deleting invoice:', error);
      }
    }
  };

  const handleSend = async (invoice: InvoiceWithDetails) => {
    try {
      await invoicesApi.sendInvoice(invoice.id);
      refetch();
    } catch (error) {
      console.error('Error sending invoice:', error);
    }
  };

  const handlePrint = (invoice: InvoiceWithDetails) => {
    // TODO: Generate and print PDF
    console.log('Print invoice:', invoice);
  };

  const handleAddInvoice = () => {
    // TODO: Open add invoice modal or navigate to add page
    console.log('Add new invoice');
  };

  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page });
  };

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">الفواتير</h1>
        </div>
        
        <div className="card">
          <div className="card-body">
            <div className="text-center py-12">
              <div className="text-red-500 mb-4">
                <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                خطأ في تحميل البيانات
              </h3>
              <p className="text-gray-500 mb-4">
                حدث خطأ أثناء تحميل قائمة الفواتير
              </p>
              <button
                onClick={() => refetch()}
                className="btn btn-primary"
              >
                إعادة المحاولة
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">الفواتير</h1>
          <p className="text-gray-600">
            إدارة وتتبع جميع الفواتير
          </p>
        </div>
        <div className="text-sm text-gray-500">
          {pagination && (
            <span>
              إجمالي الفواتير: {pagination.total}
            </span>
          )}
        </div>
      </div>

      {/* Search and Filters */}
      <SearchFilters
        filters={filters}
        onFiltersChange={setFilters}
        onAddInvoice={handleAddInvoice}
      />

      {/* Invoices Table */}
      <InvoicesTable
        invoices={invoices}
        loading={isLoading}
        onView={handleView}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onSend={handleSend}
        onPrint={handlePrint}
      />

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="card">
          <div className="card-body">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                عرض {((pagination.page - 1) * pagination.limit) + 1} إلى{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)} من{' '}
                {pagination.total} فاتورة
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                  className="btn btn-secondary btn-sm"
                >
                  السابق
                </button>
                <span className="text-sm text-gray-700">
                  صفحة {pagination.page} من {pagination.totalPages}
                </span>
                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page >= pagination.totalPages}
                  className="btn btn-secondary btn-sm"
                >
                  التالي
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
