'use client';

import { useState, useEffect } from 'react';
import { apiClient, type InvoiceWithDetails } from '../../lib/api-client';

interface InvoiceFilters {
  page: number;
  limit: number;
  search?: string;
  status?: string;
}

// Invoice Status Badge Component
const InvoiceStatusBadge = ({ status }: { status: string }) => {
  const statusMap = {
    DRAFT: { label: 'مسودة', color: '#6b7280' },
    SENT: { label: 'مرسلة', color: '#3b82f6' },
    PAID: { label: 'مدفوعة', color: '#10b981' },
    OVERDUE: { label: 'متأخرة', color: '#ef4444' },
    CANCELLED: { label: 'ملغية', color: '#6b7280' },
  };
  
  const statusInfo = statusMap[status as keyof typeof statusMap] || statusMap.DRAFT;
  return (
    <span style={{
      display: 'inline-flex',
      alignItems: 'center',
      padding: '2px 8px',
      borderRadius: '12px',
      fontSize: '12px',
      fontWeight: '500',
      backgroundColor: `${statusInfo.color}20`,
      color: statusInfo.color
    }}>
      {statusInfo.label}
    </span>
  );
};

// Invoices Table Component
interface InvoicesTableProps {
  invoices: InvoiceWithDetails[];
  loading: boolean;
  onView: (invoice: InvoiceWithDetails) => void;
  onEdit: (invoice: InvoiceWithDetails) => void;
  onDelete: (invoice: InvoiceWithDetails) => void;
  onSend: (invoice: InvoiceWithDetails) => void;
  onPrint: (invoice: InvoiceWithDetails) => void;
}

const InvoicesTable = ({ 
  invoices, 
  loading, 
  onView, 
  onEdit, 
  onDelete, 
  onSend, 
  onPrint 
}: InvoicesTableProps) => {
  if (loading) {
    return (
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        overflow: 'hidden'
      }}>
        <div style={{ padding: '48px 24px', textAlign: 'center' }}>
          <div style={{
            width: '32px',
            height: '32px',
            border: '2px solid #e5e7eb',
            borderTop: '2px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <span style={{ color: '#6b7280' }}>جاري تحميل الفواتير...</span>
        </div>
      </div>
    );
  }

  if (invoices.length === 0) {
    return (
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        overflow: 'hidden'
      }}>
        <div style={{ padding: '48px 24px', textAlign: 'center' }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>📄</div>
          <h3 style={{
            fontSize: '18px',
            fontWeight: '500',
            color: '#111827',
            marginBottom: '8px'
          }}>
            لا توجد فواتير
          </h3>
          <p style={{
            color: '#6b7280',
            marginBottom: '24px'
          }}>
            ابدأ بإنشاء فاتورتك الأولى
          </p>
          <button style={{
            display: 'inline-flex',
            alignItems: 'center',
            padding: '12px 24px',
            backgroundColor: '#2563eb',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            cursor: 'pointer'
          }}>
            <span style={{ marginLeft: '8px' }}>➕</span>
            إنشاء فاتورة جديدة
          </button>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
      overflow: 'hidden'
    }}>
      <div style={{ padding: 0 }}>
        <div style={{ overflowX: 'auto' }}>
          <table style={{
            width: '100%',
            borderCollapse: 'collapse'
          }}>
            <thead style={{ backgroundColor: '#f9fafb' }}>
              <tr>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  رقم الفاتورة
                </th>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  العميل
                </th>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  المبلغ
                </th>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  تاريخ الإصدار
                </th>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  تاريخ الاستحقاق
                </th>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  الحالة
                </th>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody style={{ backgroundColor: 'white' }}>
              {invoices.map((invoice) => {
                const isOverdue = invoice.status === 'SENT' && 
                  new Date(invoice.dueDate) < new Date();
                
                return (
                  <tr key={invoice.id} style={{
                    borderBottom: '1px solid #e5e7eb'
                  }}>
                    <td style={{
                      padding: '16px 24px'
                    }}>
                      <div style={{
                        fontWeight: '500',
                        color: '#111827'
                      }}>
                        {invoice.invoiceNumber}
                      </div>
                      <div style={{
                        fontSize: '14px',
                        color: '#6b7280'
                      }}>
                        {invoice.items?.length || 0} عنصر
                      </div>
                    </td>
                    <td style={{
                      padding: '16px 24px'
                    }}>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <div style={{
                          width: '32px',
                          height: '32px',
                          backgroundColor: '#dbeafe',
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          marginLeft: '12px'
                        }}>
                          <span style={{
                            fontSize: '14px',
                            fontWeight: '500',
                            color: '#2563eb'
                          }}>
                            {invoice.client?.name?.charAt(0) || '؟'}
                          </span>
                        </div>
                        <div>
                          <div style={{
                            fontWeight: '500',
                            color: '#111827'
                          }}>
                            {invoice.client?.name || 'غير محدد'}
                          </div>
                          {invoice.client?.email && (
                            <div style={{
                              fontSize: '14px',
                              color: '#6b7280'
                            }}>
                              {invoice.client.email}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td style={{
                      padding: '16px 24px'
                    }}>
                      <div style={{
                        fontWeight: '500',
                        color: '#111827'
                      }}>
                        {invoice.totalAmount?.toLocaleString('ar-SA')} {invoice.currency}
                      </div>
                      {invoice.payments && invoice.payments.length > 0 && (
                        <div style={{
                          fontSize: '14px',
                          color: '#10b981'
                        }}>
                          مدفوع: {invoice.payments.reduce((sum, p) => sum + p.amount, 0).toLocaleString('ar-SA')}
                        </div>
                      )}
                    </td>
                    <td style={{
                      padding: '16px 24px',
                      fontSize: '14px',
                      color: '#6b7280'
                    }}>
                      {new Date(invoice.issueDate).toLocaleDateString('ar-SA')}
                    </td>
                    <td style={{
                      padding: '16px 24px'
                    }}>
                      <div style={{
                        fontSize: '14px',
                        color: isOverdue ? '#ef4444' : '#6b7280',
                        fontWeight: isOverdue ? '500' : 'normal'
                      }}>
                        {new Date(invoice.dueDate).toLocaleDateString('ar-SA')}
                        {isOverdue && (
                          <div style={{
                            fontSize: '12px'
                          }}>
                            متأخر {Math.ceil((Date.now() - new Date(invoice.dueDate).getTime()) / (1000 * 60 * 60 * 24))} يوم
                          </div>
                        )}
                      </div>
                    </td>
                    <td style={{
                      padding: '16px 24px'
                    }}>
                      <InvoiceStatusBadge status={isOverdue ? 'OVERDUE' : invoice.status} />
                    </td>
                    <td style={{
                      padding: '16px 24px'
                    }}>
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px'
                      }}>
                        <button
                          onClick={() => onView(invoice)}
                          style={{
                            padding: '4px',
                            color: '#6b7280',
                            background: 'none',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer'
                          }}
                          title="عرض التفاصيل"
                          onMouseOver={(e) => e.target.style.color = '#2563eb'}
                          onMouseOut={(e) => e.target.style.color = '#6b7280'}
                        >
                          👁️
                        </button>
                        
                        {invoice.status === 'DRAFT' && (
                          <button
                            onClick={() => onEdit(invoice)}
                            style={{
                              padding: '4px',
                              color: '#6b7280',
                              background: 'none',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: 'pointer'
                            }}
                            title="تعديل"
                            onMouseOver={(e) => e.target.style.color = '#10b981'}
                            onMouseOut={(e) => e.target.style.color = '#6b7280'}
                          >
                            ✏️
                          </button>
                        )}
                        
                        {(invoice.status === 'DRAFT' || invoice.status === 'SENT') && (
                          <button
                            onClick={() => onSend(invoice)}
                            style={{
                              padding: '4px',
                              color: '#6b7280',
                              background: 'none',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: 'pointer'
                            }}
                            title="إرسال"
                            onMouseOver={(e) => e.target.style.color = '#3b82f6'}
                            onMouseOut={(e) => e.target.style.color = '#6b7280'}
                          >
                            📤
                          </button>
                        )}
                        
                        <button
                          onClick={() => onPrint(invoice)}
                          style={{
                            padding: '4px',
                            color: '#6b7280',
                            background: 'none',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer'
                          }}
                          title="طباعة"
                          onMouseOver={(e) => e.target.style.color = '#7c3aed'}
                          onMouseOut={(e) => e.target.style.color = '#6b7280'}
                        >
                          🖨️
                        </button>
                        
                        {invoice.status === 'DRAFT' && (
                          <button
                            onClick={() => onDelete(invoice)}
                            style={{
                              padding: '4px',
                              color: '#6b7280',
                              background: 'none',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: 'pointer'
                            }}
                            title="حذف"
                            onMouseOver={(e) => e.target.style.color = '#ef4444'}
                            onMouseOut={(e) => e.target.style.color = '#6b7280'}
                          >
                            🗑️
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

// Search and Filters Component
interface SearchFiltersProps {
  filters: InvoiceFilters;
  onFiltersChange: (filters: InvoiceFilters) => void;
  onAddInvoice: () => void;
}

const SearchFilters = ({ filters, onFiltersChange, onAddInvoice }: SearchFiltersProps) => {
  const handleSearchChange = (search: string) => {
    onFiltersChange({ ...filters, search, page: 1 });
  };

  const handleStatusChange = (status: string) => {
    onFiltersChange({ 
      ...filters, 
      status: status === 'all' ? undefined : status, 
      page: 1 
    });
  };

  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
      overflow: 'hidden'
    }}>
      <div style={{ padding: '24px' }}>
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '16px'
        }} className="lg:flex-row lg:items-center lg:justify-between">
          {/* Search */}
          <div style={{ flex: 1, maxWidth: '400px' }}>
            <div style={{ position: 'relative' }}>
              <span style={{
                position: 'absolute',
                right: '12px',
                top: '50%',
                transform: 'translateY(-50%)',
                fontSize: '16px',
                color: '#9ca3af'
              }}>
                🔍
              </span>
              <input
                type="text"
                placeholder="البحث في الفواتير..."
                style={{
                  width: '100%',
                  padding: '12px 16px 12px 44px',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '16px',
                  outline: 'none'
                }}
                value={filters.search || ''}
                onChange={(e) => handleSearchChange(e.target.value)}
                onFocus={(e) => {
                  e.target.style.borderColor = '#3b82f6';
                  e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#d1d5db';
                  e.target.style.boxShadow = 'none';
                }}
              />
            </div>
          </div>

          {/* Filters */}
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '16px'
          }} className="sm:flex-row sm:items-center">
            <select
              style={{
                padding: '12px 16px',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '14px',
                outline: 'none',
                backgroundColor: 'white'
              }}
              value={filters.status || 'all'}
              onChange={(e) => handleStatusChange(e.target.value)}
            >
              <option value="all">جميع الفواتير</option>
              <option value="DRAFT">المسودات</option>
              <option value="SENT">المرسلة</option>
              <option value="PAID">المدفوعة</option>
              <option value="OVERDUE">المتأخرة</option>
              <option value="CANCELLED">الملغية</option>
            </select>

            <button
              onClick={onAddInvoice}
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                padding: '12px 24px',
                backgroundColor: '#2563eb',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'background-color 0.2s'
              }}
              onMouseOver={(e) => e.target.style.backgroundColor = '#1d4ed8'}
              onMouseOut={(e) => e.target.style.backgroundColor = '#2563eb'}
            >
              <span style={{ marginLeft: '8px' }}>➕</span>
              إنشاء فاتورة
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function InvoicesPage() {
  const [filters, setFilters] = useState<InvoiceFilters>({
    page: 1,
    limit: 10,
    search: '',
    status: undefined,
  });

  const [invoices, setInvoices] = useState<InvoiceWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<any>(null);

  // Fetch invoices
  useEffect(() => {
    const fetchInvoices = async () => {
      try {
        setLoading(true);
        const response = await apiClient.getInvoices(filters);
        if (response.success) {
          setInvoices(response.data || []);
          setPagination(response.pagination);
        } else {
          setError(response.message);
        }
      } catch (err: any) {
        setError(err.message || 'حدث خطأ في تحميل الفواتير');
      } finally {
        setLoading(false);
      }
    };

    fetchInvoices();
  }, [filters]);

  const handleView = (invoice: InvoiceWithDetails) => {
    alert(`عرض تفاصيل الفاتورة: ${invoice.invoiceNumber}`);
  };

  const handleEdit = (invoice: InvoiceWithDetails) => {
    alert(`تعديل الفاتورة: ${invoice.invoiceNumber}`);
  };

  const handleDelete = async (invoice: InvoiceWithDetails) => {
    if (confirm(`هل أنت متأكد من حذف الفاتورة "${invoice.invoiceNumber}"؟`)) {
      try {
        await apiClient.deleteInvoice(invoice.id);
        // Refresh the list
        const response = await apiClient.getInvoices(filters);
        if (response.success) {
          setInvoices(response.data || []);
          setPagination(response.pagination);
        }
      } catch (error) {
        alert('حدث خطأ أثناء حذف الفاتورة');
      }
    }
  };

  const handleSend = async (invoice: InvoiceWithDetails) => {
    try {
      await apiClient.sendInvoice(invoice.id);
      // Refresh the list
      const response = await apiClient.getInvoices(filters);
      if (response.success) {
        setInvoices(response.data || []);
        setPagination(response.pagination);
      }
      alert('تم إرسال الفاتورة بنجاح');
    } catch (error) {
      alert('حدث خطأ أثناء إرسال الفاتورة');
    }
  };

  const handlePrint = (invoice: InvoiceWithDetails) => {
    alert(`طباعة الفاتورة: ${invoice.invoiceNumber}`);
  };

  const handleAddInvoice = () => {
    alert('إنشاء فاتورة جديدة');
  };

  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page });
  };

  if (error) {
    return (
      <div style={{
        fontFamily: 'Cairo, Arial, sans-serif',
        direction: 'rtl'
      }}>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <h1 style={{
              fontSize: '28px',
              fontWeight: 'bold',
              color: '#111827',
              margin: 0
            }}>
              الفواتير
            </h1>
          </div>
          
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
            overflow: 'hidden'
          }}>
            <div style={{ padding: '48px 24px', textAlign: 'center' }}>
              <div style={{ fontSize: '48px', color: '#ef4444', marginBottom: '16px' }}>⚠️</div>
              <h3 style={{
                fontSize: '18px',
                fontWeight: '500',
                color: '#111827',
                marginBottom: '8px'
              }}>
                خطأ في تحميل البيانات
              </h3>
              <p style={{
                color: '#6b7280',
                marginBottom: '16px'
              }}>
                {error}
              </p>
              <button
                onClick={() => window.location.reload()}
                style={{
                  padding: '12px 24px',
                  backgroundColor: '#2563eb',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}
              >
                إعادة المحاولة
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: 'rtl'
    }}>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <div>
            <h1 style={{
              fontSize: '28px',
              fontWeight: 'bold',
              color: '#111827',
              margin: '0 0 4px 0'
            }}>
              الفواتير
            </h1>
            <p style={{
              color: '#6b7280',
              margin: 0
            }}>
              إدارة وتتبع جميع الفواتير
            </p>
          </div>
          <div style={{
            fontSize: '14px',
            color: '#6b7280'
          }}>
            {pagination && (
              <span>
                إجمالي الفواتير: {pagination.total}
              </span>
            )}
          </div>
        </div>

        {/* Search and Filters */}
        <SearchFilters
          filters={filters}
          onFiltersChange={setFilters}
          onAddInvoice={handleAddInvoice}
        />

        {/* Invoices Table */}
        <InvoicesTable
          invoices={invoices}
          loading={loading}
          onView={handleView}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onSend={handleSend}
          onPrint={handlePrint}
        />

        {/* Pagination */}
        {pagination && pagination.totalPages > 1 && (
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
            overflow: 'hidden'
          }}>
            <div style={{ padding: '24px' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}>
                <div style={{
                  fontSize: '14px',
                  color: '#374151'
                }}>
                  عرض {((pagination.page - 1) * pagination.limit) + 1} إلى{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} من{' '}
                  {pagination.total} فاتورة
                </div>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <button
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page <= 1}
                    style={{
                      padding: '8px 16px',
                      backgroundColor: pagination.page <= 1 ? '#f3f4f6' : '#2563eb',
                      color: pagination.page <= 1 ? '#9ca3af' : 'white',
                      border: 'none',
                      borderRadius: '6px',
                      fontSize: '14px',
                      cursor: pagination.page <= 1 ? 'not-allowed' : 'pointer'
                    }}
                  >
                    السابق
                  </button>
                  <span style={{
                    fontSize: '14px',
                    color: '#374151'
                  }}>
                    صفحة {pagination.page} من {pagination.totalPages}
                  </span>
                  <button
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page >= pagination.totalPages}
                    style={{
                      padding: '8px 16px',
                      backgroundColor: pagination.page >= pagination.totalPages ? '#f3f4f6' : '#2563eb',
                      color: pagination.page >= pagination.totalPages ? '#9ca3af' : 'white',
                      border: 'none',
                      borderRadius: '6px',
                      fontSize: '14px',
                      cursor: pagination.page >= pagination.totalPages ? 'not-allowed' : 'pointer'
                    }}
                  >
                    التالي
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Add CSS animation */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}
