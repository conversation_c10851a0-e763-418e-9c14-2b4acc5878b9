'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth, useAuthActions } from '../store/auth-store';

// Navigation items
const navigationItems = [
  {
    id: 'dashboard',
    label: 'لوحة التحكم',
    href: '/dashboard',
    icon: '🏠',
  },
  {
    id: 'clients',
    label: 'العملاء',
    href: '/dashboard/clients',
    icon: '👥',
  },
  {
    id: 'invoices',
    label: 'الفواتير',
    href: '/dashboard/invoices',
    icon: '📄',
  },
  {
    id: 'reports',
    label: 'التقارير',
    href: '/dashboard/reports',
    icon: '📊',
  },
  {
    id: 'settings',
    label: 'الإعدادات',
    href: '/dashboard/settings',
    icon: '⚙️',
  },
];

// Sidebar Component
interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const Sidebar = ({ isOpen, onClose }: SidebarProps) => {
  const pathname = usePathname();
  const { user, tenant } = useAuth();
  const { logout } = useAuthActions();

  const handleLogout = async () => {
    logout();
  };

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          style={{
            position: 'fixed',
            inset: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 20
          }}
          className="lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div
        style={{
          position: 'fixed',
          top: 0,
          right: 0,
          bottom: 0,
          width: '256px',
          backgroundColor: 'white',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
          transform: isOpen ? 'translateX(0)' : 'translateX(100%)',
          transition: 'transform 0.3s ease-in-out',
          zIndex: 30,
          display: 'flex',
          flexDirection: 'column',
          fontFamily: 'Cairo, Arial, sans-serif',
          direction: 'rtl'
        }}
        className="lg:translate-x-0 lg:static lg:inset-0"
      >
        {/* Header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          height: '64px',
          padding: '0 24px',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{
              width: '32px',
              height: '32px',
              background: 'linear-gradient(135deg, #2563eb 0%, #4f46e5 100%)',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginLeft: '12px'
            }}>
              <span style={{ fontSize: '16px' }}>📄</span>
            </div>
            <span style={{
              fontSize: '18px',
              fontWeight: '600',
              color: '#111827'
            }}>
              الفواتير
            </span>
          </div>
          <button
            onClick={onClose}
            style={{
              padding: '4px',
              borderRadius: '6px',
              color: '#6b7280',
              background: 'none',
              border: 'none',
              cursor: 'pointer'
            }}
            className="lg:hidden"
          >
            ✕
          </button>
        </div>

        {/* User Info */}
        <div style={{
          padding: '16px 24px',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{
              width: '40px',
              height: '40px',
              backgroundColor: '#e5e7eb',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginLeft: '12px'
            }}>
              <span style={{ fontSize: '18px' }}>👤</span>
            </div>
            <div style={{ flex: 1, minWidth: 0 }}>
              <p style={{
                fontSize: '14px',
                fontWeight: '500',
                color: '#111827',
                margin: 0,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}>
                {user?.firstName} {user?.lastName}
              </p>
              <p style={{
                fontSize: '12px',
                color: '#6b7280',
                margin: 0,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}>
                {tenant?.name}
              </p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav style={{
          flex: 1,
          padding: '16px',
          overflowY: 'auto'
        }}>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            {navigationItems.map((item) => {
              const isActive = pathname === item.href;

              return (
                <Link
                  key={item.id}
                  href={item.href}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    padding: '12px 16px',
                    fontSize: '14px',
                    fontWeight: '500',
                    borderRadius: '8px',
                    textDecoration: 'none',
                    transition: 'all 0.2s',
                    backgroundColor: isActive ? '#dbeafe' : 'transparent',
                    color: isActive ? '#1d4ed8' : '#6b7280',
                    borderRight: isActive ? '4px solid #2563eb' : '4px solid transparent'
                  }}
                  onClick={() => {
                    // Close mobile sidebar when navigating
                    if (window.innerWidth < 1024) {
                      onClose();
                    }
                  }}
                  onMouseOver={(e) => {
                    if (!isActive) {
                      e.target.style.backgroundColor = '#f9fafb';
                      e.target.style.color = '#111827';
                    }
                  }}
                  onMouseOut={(e) => {
                    if (!isActive) {
                      e.target.style.backgroundColor = 'transparent';
                      e.target.style.color = '#6b7280';
                    }
                  }}
                >
                  <span style={{
                    marginLeft: '12px',
                    fontSize: '16px'
                  }}>
                    {item.icon}
                  </span>
                  {item.label}
                </Link>
              );
            })}
          </div>
        </nav>

        {/* Footer */}
        <div style={{
          padding: '16px',
          borderTop: '1px solid #e5e7eb'
        }}>
          <button
            onClick={handleLogout}
            style={{
              display: 'flex',
              alignItems: 'center',
              width: '100%',
              padding: '12px 16px',
              fontSize: '14px',
              fontWeight: '500',
              color: '#6b7280',
              borderRadius: '8px',
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}
            onMouseOver={(e) => {
              e.target.style.backgroundColor = '#f9fafb';
              e.target.style.color = '#111827';
            }}
            onMouseOut={(e) => {
              e.target.style.backgroundColor = 'transparent';
              e.target.style.color = '#6b7280';
            }}
          >
            <span style={{
              marginLeft: '12px',
              fontSize: '16px'
            }}>
              🚪
            </span>
            تسجيل الخروج
          </button>
        </div>
      </div>
    </>
  );
};

// Header Component
interface HeaderProps {
  onMenuClick: () => void;
}

const Header = ({ onMenuClick }: HeaderProps) => {
  const { user } = useAuth();

  return (
    <header style={{
      backgroundColor: 'white',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
      borderBottom: '1px solid #e5e7eb'
    }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        height: '64px',
        padding: '0 16px',
        fontFamily: 'Cairo, Arial, sans-serif',
        direction: 'rtl'
      }}>
        {/* Mobile menu button */}
        <button
          onClick={onMenuClick}
          style={{
            padding: '8px',
            borderRadius: '6px',
            color: '#6b7280',
            background: 'none',
            border: 'none',
            cursor: 'pointer'
          }}
          className="lg:hidden"
          onMouseOver={(e) => {
            e.target.style.backgroundColor = '#f3f4f6';
            e.target.style.color = '#374151';
          }}
          onMouseOut={(e) => {
            e.target.style.backgroundColor = 'transparent';
            e.target.style.color = '#6b7280';
          }}
        >
          ☰
        </button>

        {/* Page title - will be updated by individual pages */}
        <div style={{ flex: 1 }}>
          <h1 style={{
            fontSize: '20px',
            fontWeight: '600',
            color: '#111827',
            margin: 0
          }} className="lg:hidden">
            نظام إدارة الفواتير
          </h1>
        </div>

        {/* Right side */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '16px'
        }}>
          {/* Notifications */}
          <button style={{
            padding: '8px',
            color: '#6b7280',
            background: 'none',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer'
          }}>
            🔔
          </button>

          {/* User menu */}
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{
              width: '32px',
              height: '32px',
              backgroundColor: '#e5e7eb',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <span style={{ fontSize: '14px' }}>👤</span>
            </div>
            <span style={{
              marginRight: '8px',
              fontSize: '14px',
              fontWeight: '500',
              color: '#374151'
            }} className="hidden sm:block">
              {user?.firstName}
            </span>
          </div>
        </div>
      </div>
    </header>
  );
};

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();
  const { getProfile } = useAuthActions();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Check authentication and get user profile
  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        router.push('/auth/login');
      } else {
        getProfile();
      }
    }
  }, [isAuthenticated, isLoading, router, getProfile]);

  // Show loading while checking authentication
  if (isLoading || !isAuthenticated) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontFamily: 'Cairo, Arial, sans-serif'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '32px',
            height: '32px',
            border: '2px solid #e5e7eb',
            borderTop: '2px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <p style={{ color: '#6b7280' }}>جاري التحميل...</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      display: 'flex',
      height: '100vh',
      backgroundColor: '#f9fafb',
      fontFamily: 'Cairo, Arial, sans-serif'
    }}>
      {/* Sidebar */}
      <Sidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
      />

      {/* Main content */}
      <div style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }} className="lg:mr-64">
        {/* Header */}
        <Header onMenuClick={() => setSidebarOpen(true)} />

        {/* Page content */}
        <main style={{
          flex: 1,
          overflowY: 'auto'
        }}>
          <div style={{
            padding: '16px'
          }} className="sm:p-6 lg:p-8">
            {children}
          </div>
        </main>
      </div>

      {/* Add CSS animation */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}
