// API Client for Invoice Management System

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

// Types
export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Tenant {
  id: string;
  name: string;
  domain: string;
  createdAt: string;
  updatedAt: string;
}

export interface Client {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  taxNumber?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface Payment {
  id: string;
  amount: number;
  paymentDate: string;
  method: string;
  notes?: string;
}

export interface InvoiceWithDetails {
  id: string;
  invoiceNumber: string;
  clientId: string;
  client?: Client;
  issueDate: string;
  dueDate: string;
  status: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED';
  subtotal: number;
  taxRate: number;
  taxAmount: number;
  totalAmount: number;
  currency: string;
  notes?: string;
  terms?: string;
  items?: InvoiceItem[];
  payments?: Payment[];
  createdAt: string;
  updatedAt: string;
}

export interface DashboardStats {
  totalClients: number;
  totalInvoices: number;
  totalRevenue: number;
  thisMonthRevenue: number;
  lastMonthRevenue: number;
  pendingAmount: number;
  overdueInvoices: number;
  paidInvoices: number;
  recentInvoices: InvoiceWithDetails[];
  topClients: Array<{
    id: string;
    name: string;
    totalInvoices: number;
    totalAmount: number;
    paidAmount: number;
  }>;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// API Client Class
class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    // Get token from localStorage if available
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('auth_token');
    }
  }

  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    return headers;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`;
      const response = await fetch(url, {
        ...options,
        headers: {
          ...this.getHeaders(),
          ...options.headers,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'حدث خطأ في الطلب');
      }

      return data;
    } catch (error) {
      console.error('API Request Error:', error);
      throw error;
    }
  }

  // Auth methods
  setToken(token: string) {
    this.token = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token);
    }
  }

  clearToken() {
    this.token = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
    }
  }

  async login(email: string, password: string): Promise<ApiResponse<{ user: User; tenant: Tenant; token: string }>> {
    // Mock login for development
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay

    // Demo credentials
    if (email === '<EMAIL>' && password === 'admin123') {
      const mockUser: User = {
        id: '1',
        firstName: 'أحمد',
        lastName: 'محمد',
        email: '<EMAIL>',
        phone: '+966501234567',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      };

      const mockTenant: Tenant = {
        id: '1',
        name: 'شركة الأمل للتجارة',
        domain: 'alamal',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      };

      const mockToken = 'mock-jwt-token-' + Date.now();

      return {
        success: true,
        data: { user: mockUser, tenant: mockTenant, token: mockToken },
        message: 'تم تسجيل الدخول بنجاح'
      };
    } else {
      return {
        success: false,
        message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
      };
    }
  }

  async register(userData: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    companyName: string;
    phone?: string;
  }): Promise<ApiResponse<{ user: User; tenant: Tenant; token: string }>> {
    // Mock registration for development
    await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate network delay

    const mockUser: User = {
      id: Date.now().toString(),
      firstName: userData.firstName,
      lastName: userData.lastName,
      email: userData.email,
      phone: userData.phone,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const mockTenant: Tenant = {
      id: Date.now().toString(),
      name: userData.companyName,
      domain: userData.companyName.toLowerCase().replace(/\s+/g, '-'),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const mockToken = 'mock-jwt-token-' + Date.now();

    return {
      success: true,
      data: { user: mockUser, tenant: mockTenant, token: mockToken },
      message: 'تم إنشاء الحساب بنجاح'
    };
  }

  async logout(): Promise<ApiResponse> {
    // Mock logout for development
    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      success: true,
      message: 'تم تسجيل الخروج بنجاح'
    };
  }

  async getProfile(): Promise<ApiResponse<{ user: User; tenant: Tenant }>> {
    // Mock profile fetch for development
    await new Promise(resolve => setTimeout(resolve, 500));

    const mockUser: User = {
      id: '1',
      firstName: 'أحمد',
      lastName: 'محمد',
      email: '<EMAIL>',
      phone: '+966501234567',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    };

    const mockTenant: Tenant = {
      id: '1',
      name: 'شركة الأمل للتجارة',
      domain: 'alamal',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    };

    return {
      success: true,
      data: { user: mockUser, tenant: mockTenant },
      message: 'تم جلب بيانات المستخدم بنجاح'
    };
  }

  // Dashboard methods
  async getDashboardStats(): Promise<ApiResponse<DashboardStats>> {
    // Simulate very short delay for better UX
    await new Promise(resolve => setTimeout(resolve, 300));

    // Mock data for development
    const mockStats: DashboardStats = {
      totalClients: 25,
      totalInvoices: 150,
      totalRevenue: 125000,
      thisMonthRevenue: 18000,
      lastMonthRevenue: 15000,
      pendingAmount: 35000,
      overdueInvoices: 8,
      paidInvoices: 120,
      recentInvoices: [
        {
          id: '1',
          invoiceNumber: 'INV-2024-001',
          clientId: '1',
          client: { id: '1', name: 'شركة الأمل للتجارة', email: '<EMAIL>', isActive: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
          issueDate: '2024-01-15',
          dueDate: '2024-02-15',
          status: 'SENT',
          subtotal: 10000,
          taxRate: 15,
          taxAmount: 1500,
          totalAmount: 11500,
          currency: 'SAR',
          createdAt: '2024-01-15',
          updatedAt: '2024-01-15'
        },
        {
          id: '2',
          invoiceNumber: 'INV-2024-002',
          clientId: '2',
          client: { id: '2', name: 'مؤسسة النور', email: '<EMAIL>', isActive: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
          issueDate: '2024-01-20',
          dueDate: '2024-02-20',
          status: 'PAID',
          subtotal: 8000,
          taxRate: 15,
          taxAmount: 1200,
          totalAmount: 9200,
          currency: 'SAR',
          createdAt: '2024-01-20',
          updatedAt: '2024-01-20'
        }
      ],
      topClients: [
        { id: '1', name: 'شركة الأمل للتجارة', totalInvoices: 15, totalAmount: 85000, paidAmount: 70000 },
        { id: '2', name: 'مؤسسة النور', totalInvoices: 12, totalAmount: 65000, paidAmount: 65000 },
        { id: '3', name: 'شركة الفجر', totalInvoices: 8, totalAmount: 45000, paidAmount: 30000 }
      ]
    };

    return Promise.resolve({
      success: true,
      data: mockStats,
      message: 'تم جلب الإحصائيات بنجاح'
    });
  }

  // Clients methods
  async getClients(filters?: {
    page?: number;
    limit?: number;
    search?: string;
    isActive?: boolean;
  }): Promise<ApiResponse<Client[]>> {
    const queryParams = new URLSearchParams();
    if (filters?.page) queryParams.append('page', filters.page.toString());
    if (filters?.limit) queryParams.append('limit', filters.limit.toString());
    if (filters?.search) queryParams.append('search', filters.search);
    if (filters?.isActive !== undefined) queryParams.append('isActive', filters.isActive.toString());

    // Mock data for development
    const mockClients: Client[] = [
      {
        id: '1',
        name: 'شركة الأمل للتجارة',
        email: '<EMAIL>',
        phone: '+966501234567',
        address: 'شارع الملك فهد، الرياض',
        city: 'الرياض',
        country: 'المملكة العربية السعودية',
        taxNumber: '*********',
        isActive: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01'
      },
      {
        id: '2',
        name: 'مؤسسة النور',
        email: '<EMAIL>',
        phone: '+966507654321',
        address: 'طريق الملك عبدالعزيز، جدة',
        city: 'جدة',
        country: 'المملكة العربية السعودية',
        taxNumber: '*********',
        isActive: true,
        createdAt: '2024-01-02',
        updatedAt: '2024-01-02'
      }
    ];

    return Promise.resolve({
      success: true,
      data: mockClients,
      message: 'تم جلب العملاء بنجاح',
      pagination: {
        page: filters?.page || 1,
        limit: filters?.limit || 10,
        total: mockClients.length,
        totalPages: 1
      }
    });
  }

  async deleteClient(id: string): Promise<ApiResponse> {
    // Mock delete for development
    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      success: true,
      message: 'تم حذف العميل بنجاح'
    };
  }

  // Invoices methods
  async getInvoices(filters?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
  }): Promise<ApiResponse<InvoiceWithDetails[]>> {
    const queryParams = new URLSearchParams();
    if (filters?.page) queryParams.append('page', filters.page.toString());
    if (filters?.limit) queryParams.append('limit', filters.limit.toString());
    if (filters?.search) queryParams.append('search', filters.search);
    if (filters?.status) queryParams.append('status', filters.status);

    // Mock data for development
    const mockInvoices: InvoiceWithDetails[] = [
      {
        id: '1',
        invoiceNumber: 'INV-2024-001',
        clientId: '1',
        client: { id: '1', name: 'شركة الأمل للتجارة', email: '<EMAIL>', isActive: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
        issueDate: '2024-01-15',
        dueDate: '2024-02-15',
        status: 'SENT',
        subtotal: 10000,
        taxRate: 15,
        taxAmount: 1500,
        totalAmount: 11500,
        currency: 'SAR',
        notes: 'فاتورة خدمات استشارية',
        items: [
          { id: '1', description: 'استشارة تقنية', quantity: 10, unitPrice: 1000, totalPrice: 10000 }
        ],
        createdAt: '2024-01-15',
        updatedAt: '2024-01-15'
      },
      {
        id: '2',
        invoiceNumber: 'INV-2024-002',
        clientId: '2',
        client: { id: '2', name: 'مؤسسة النور', email: '<EMAIL>', isActive: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
        issueDate: '2024-01-20',
        dueDate: '2024-02-20',
        status: 'PAID',
        subtotal: 8000,
        taxRate: 15,
        taxAmount: 1200,
        totalAmount: 9200,
        currency: 'SAR',
        items: [
          { id: '2', description: 'تطوير موقع إلكتروني', quantity: 1, unitPrice: 8000, totalPrice: 8000 }
        ],
        payments: [
          { id: '1', amount: 9200, paymentDate: '2024-01-25', method: 'bank_transfer', notes: 'تم الدفع بالتحويل البنكي' }
        ],
        createdAt: '2024-01-20',
        updatedAt: '2024-01-25'
      }
    ];

    return Promise.resolve({
      success: true,
      data: mockInvoices,
      message: 'تم جلب الفواتير بنجاح',
      pagination: {
        page: filters?.page || 1,
        limit: filters?.limit || 10,
        total: mockInvoices.length,
        totalPages: 1
      }
    });
  }

  async deleteInvoice(id: string): Promise<ApiResponse> {
    // Mock delete for development
    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      success: true,
      message: 'تم حذف الفاتورة بنجاح'
    };
  }

  async sendInvoice(id: string): Promise<ApiResponse> {
    // Mock send for development
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      success: true,
      message: 'تم إرسال الفاتورة بنجاح'
    };
  }
}

// Export singleton instance
export const apiClient = new ApiClient(API_BASE_URL);
export default apiClient;
