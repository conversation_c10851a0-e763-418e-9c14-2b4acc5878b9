'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  UsersIcon,
} from '@heroicons/react/24/outline';
import { clientsApi } from '@/lib/api';
import type { Client, ClientFilters } from '@/types';

// Clients Table Component
interface ClientsTableProps {
  clients: Client[];
  loading: boolean;
  onView: (client: Client) => void;
  onEdit: (client: Client) => void;
  onDelete: (client: Client) => void;
}

const ClientsTable = ({ clients, loading, onView, onEdit, onDelete }: ClientsTableProps) => {
  if (loading) {
    return (
      <div className="card">
        <div className="card-body">
          <div className="flex items-center justify-center py-12">
            <div className="loading-spinner w-8 h-8 me-3"></div>
            <span className="text-gray-600">جاري تحميل العملاء...</span>
          </div>
        </div>
      </div>
    );
  }

  if (clients.length === 0) {
    return (
      <div className="card">
        <div className="card-body">
          <div className="text-center py-12">
            <UsersIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              لا توجد عملاء
            </h3>
            <p className="text-gray-500 mb-6">
              ابدأ بإضافة عملائك لإدارة فواتيرهم
            </p>
            <button className="btn btn-primary">
              <PlusIcon className="w-4 h-4 me-2" />
              إضافة عميل جديد
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="card-body p-0">
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="table-header-cell">اسم العميل</th>
                <th className="table-header-cell">البريد الإلكتروني</th>
                <th className="table-header-cell">الهاتف</th>
                <th className="table-header-cell">المدينة</th>
                <th className="table-header-cell">تاريخ الإضافة</th>
                <th className="table-header-cell">الحالة</th>
                <th className="table-header-cell">الإجراءات</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {clients.map((client) => (
                <tr key={client.id} className="table-row">
                  <td className="table-cell">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center me-3">
                        <span className="text-sm font-medium text-blue-600">
                          {client.name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {client.name}
                        </div>
                        {client.taxNumber && (
                          <div className="text-sm text-gray-500">
                            الرقم الضريبي: {client.taxNumber}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="table-cell">
                    {client.email ? (
                      <a
                        href={`mailto:${client.email}`}
                        className="text-blue-600 hover:text-blue-500"
                      >
                        {client.email}
                      </a>
                    ) : (
                      <span className="text-gray-400">غير محدد</span>
                    )}
                  </td>
                  <td className="table-cell">
                    {client.phone ? (
                      <a
                        href={`tel:${client.phone}`}
                        className="text-blue-600 hover:text-blue-500"
                      >
                        {client.phone}
                      </a>
                    ) : (
                      <span className="text-gray-400">غير محدد</span>
                    )}
                  </td>
                  <td className="table-cell">
                    {client.city || <span className="text-gray-400">غير محدد</span>}
                  </td>
                  <td className="table-cell text-gray-500">
                    {new Date(client.createdAt).toLocaleDateString('ar-SA')}
                  </td>
                  <td className="table-cell">
                    <span
                      className={`badge ${
                        client.isActive ? 'badge-success' : 'badge-gray'
                      }`}
                    >
                      {client.isActive ? 'نشط' : 'غير نشط'}
                    </span>
                  </td>
                  <td className="table-cell">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <button
                        onClick={() => onView(client)}
                        className="p-1 text-gray-400 hover:text-blue-600"
                        title="عرض التفاصيل"
                      >
                        <EyeIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => onEdit(client)}
                        className="p-1 text-gray-400 hover:text-green-600"
                        title="تعديل"
                      >
                        <PencilIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => onDelete(client)}
                        className="p-1 text-gray-400 hover:text-red-600"
                        title="حذف"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

// Search and Filters Component
interface SearchFiltersProps {
  filters: ClientFilters;
  onFiltersChange: (filters: ClientFilters) => void;
  onAddClient: () => void;
}

const SearchFilters = ({ filters, onFiltersChange, onAddClient }: SearchFiltersProps) => {
  const handleSearchChange = (search: string) => {
    onFiltersChange({ ...filters, search, page: 1 });
  };

  const handleStatusChange = (isActive: boolean | undefined) => {
    onFiltersChange({ ...filters, isActive, page: 1 });
  };

  return (
    <div className="card">
      <div className="card-body">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          {/* Search */}
          <div className="flex-1 max-w-md">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="البحث في العملاء..."
                className="form-input pr-10"
                value={filters.search || ''}
                onChange={(e) => handleSearchChange(e.target.value)}
              />
            </div>
          </div>

          {/* Filters */}
          <div className="flex items-center gap-4">
            <select
              className="form-input"
              value={filters.isActive === undefined ? 'all' : filters.isActive.toString()}
              onChange={(e) => {
                const value = e.target.value;
                handleStatusChange(
                  value === 'all' ? undefined : value === 'true'
                );
              }}
            >
              <option value="all">جميع العملاء</option>
              <option value="true">العملاء النشطون</option>
              <option value="false">العملاء غير النشطين</option>
            </select>

            <button
              onClick={onAddClient}
              className="btn btn-primary"
            >
              <PlusIcon className="w-4 h-4 me-2" />
              إضافة عميل
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function ClientsPage() {
  const [filters, setFilters] = useState<ClientFilters>({
    page: 1,
    limit: 10,
    search: '',
    isActive: undefined,
  });

  // Fetch clients
  const {
    data: clientsResponse,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['clients', filters],
    queryFn: () => clientsApi.getClients(filters),
  });

  const clients = clientsResponse?.data || [];
  const pagination = clientsResponse?.pagination;

  const handleView = (client: Client) => {
    // TODO: Open client details modal or navigate to details page
    console.log('View client:', client);
  };

  const handleEdit = (client: Client) => {
    // TODO: Open edit client modal or navigate to edit page
    console.log('Edit client:', client);
  };

  const handleDelete = async (client: Client) => {
    if (confirm(`هل أنت متأكد من حذف العميل "${client.name}"؟`)) {
      try {
        await clientsApi.deleteClient(client.id);
        refetch();
      } catch (error) {
        console.error('Error deleting client:', error);
      }
    }
  };

  const handleAddClient = () => {
    // TODO: Open add client modal or navigate to add page
    console.log('Add new client');
  };

  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page });
  };

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">العملاء</h1>
        </div>
        
        <div className="card">
          <div className="card-body">
            <div className="text-center py-12">
              <div className="text-red-500 mb-4">
                <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                خطأ في تحميل البيانات
              </h3>
              <p className="text-gray-500 mb-4">
                حدث خطأ أثناء تحميل قائمة العملاء
              </p>
              <button
                onClick={() => refetch()}
                className="btn btn-primary"
              >
                إعادة المحاولة
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">العملاء</h1>
          <p className="text-gray-600">
            إدارة قائمة العملاء ومعلوماتهم
          </p>
        </div>
        <div className="text-sm text-gray-500">
          {pagination && (
            <span>
              إجمالي العملاء: {pagination.total}
            </span>
          )}
        </div>
      </div>

      {/* Search and Filters */}
      <SearchFilters
        filters={filters}
        onFiltersChange={setFilters}
        onAddClient={handleAddClient}
      />

      {/* Clients Table */}
      <ClientsTable
        clients={clients}
        loading={isLoading}
        onView={handleView}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="card">
          <div className="card-body">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                عرض {((pagination.page - 1) * pagination.limit) + 1} إلى{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)} من{' '}
                {pagination.total} عميل
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                  className="btn btn-secondary btn-sm"
                >
                  السابق
                </button>
                <span className="text-sm text-gray-700">
                  صفحة {pagination.page} من {pagination.totalPages}
                </span>
                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page >= pagination.totalPages}
                  className="btn btn-secondary btn-sm"
                >
                  التالي
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
