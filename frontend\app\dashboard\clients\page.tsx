'use client';

import { useState, useEffect } from 'react';
import { apiClient, type Client } from '../../lib/api-client';
import ClientModal from './components/ClientModal';
import ClientViewModal from './components/ClientViewModal';
import DeleteConfirmModal from './components/DeleteConfirmModal';

interface ClientFilters {
  page: number;
  limit: number;
  search?: string;
  isActive?: boolean;
}

// Clients Table Component
interface ClientsTableProps {
  clients: Client[];
  loading: boolean;
  onView: (client: Client) => void;
  onEdit: (client: Client) => void;
  onDelete: (client: Client) => void;
}

const ClientsTable = ({ clients, loading, onView, onEdit, onDelete }: ClientsTableProps) => {
  if (loading) {
    return (
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        overflow: 'hidden'
      }}>
        <div style={{ padding: '48px 24px', textAlign: 'center' }}>
          <div style={{
            width: '32px',
            height: '32px',
            border: '2px solid #e5e7eb',
            borderTop: '2px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <span style={{ color: '#6b7280' }}>جاري تحميل العملاء...</span>
        </div>
      </div>
    );
  }

  if (clients.length === 0) {
    return (
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        overflow: 'hidden'
      }}>
        <div style={{ padding: '48px 24px', textAlign: 'center' }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>👥</div>
          <h3 style={{
            fontSize: '18px',
            fontWeight: '500',
            color: '#111827',
            marginBottom: '8px'
          }}>
            لا توجد عملاء
          </h3>
          <p style={{
            color: '#6b7280',
            marginBottom: '24px'
          }}>
            ابدأ بإضافة عملائك لإدارة فواتيرهم
          </p>
          <button style={{
            display: 'inline-flex',
            alignItems: 'center',
            padding: '12px 24px',
            backgroundColor: '#2563eb',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            cursor: 'pointer'
          }}>
            <span style={{ marginLeft: '8px' }}>➕</span>
            إضافة عميل جديد
          </button>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
      overflow: 'hidden'
    }}>
      <div style={{ padding: 0 }}>
        <div style={{ overflowX: 'auto' }}>
          <table style={{
            width: '100%',
            borderCollapse: 'collapse'
          }}>
            <thead style={{ backgroundColor: '#f9fafb' }}>
              <tr>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  اسم العميل
                </th>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  البريد الإلكتروني
                </th>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  الهاتف
                </th>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  المدينة
                </th>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  تاريخ الإضافة
                </th>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  الحالة
                </th>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody style={{ backgroundColor: 'white' }}>
              {clients.map((client) => (
                <tr key={client.id} style={{
                  borderBottom: '1px solid #e5e7eb'
                }}>
                  <td style={{
                    padding: '16px 24px'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <div style={{
                        width: '32px',
                        height: '32px',
                        backgroundColor: '#dbeafe',
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginLeft: '12px'
                      }}>
                        <span style={{
                          fontSize: '14px',
                          fontWeight: '500',
                          color: '#2563eb'
                        }}>
                          {client.name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <div style={{
                          fontWeight: '500',
                          color: '#111827'
                        }}>
                          {client.name}
                        </div>
                        {client.taxNumber && (
                          <div style={{
                            fontSize: '14px',
                            color: '#6b7280'
                          }}>
                            الرقم الضريبي: {client.taxNumber}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td style={{
                    padding: '16px 24px',
                    fontSize: '14px'
                  }}>
                    {client.email ? (
                      <a
                        href={`mailto:${client.email}`}
                        style={{
                          color: '#2563eb',
                          textDecoration: 'none'
                        }}
                        onMouseOver={(e) => e.target.style.textDecoration = 'underline'}
                        onMouseOut={(e) => e.target.style.textDecoration = 'none'}
                      >
                        {client.email}
                      </a>
                    ) : (
                      <span style={{ color: '#9ca3af' }}>غير محدد</span>
                    )}
                  </td>
                  <td style={{
                    padding: '16px 24px',
                    fontSize: '14px'
                  }}>
                    {client.phone ? (
                      <a
                        href={`tel:${client.phone}`}
                        style={{
                          color: '#2563eb',
                          textDecoration: 'none'
                        }}
                        onMouseOver={(e) => e.target.style.textDecoration = 'underline'}
                        onMouseOut={(e) => e.target.style.textDecoration = 'none'}
                      >
                        {client.phone}
                      </a>
                    ) : (
                      <span style={{ color: '#9ca3af' }}>غير محدد</span>
                    )}
                  </td>
                  <td style={{
                    padding: '16px 24px',
                    fontSize: '14px',
                    color: '#111827'
                  }}>
                    {client.city || <span style={{ color: '#9ca3af' }}>غير محدد</span>}
                  </td>
                  <td style={{
                    padding: '16px 24px',
                    fontSize: '14px',
                    color: '#6b7280'
                  }}>
                    {new Date(client.createdAt).toLocaleDateString('ar-SA')}
                  </td>
                  <td style={{
                    padding: '16px 24px',
                    fontSize: '14px'
                  }}>
                    <span style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      padding: '2px 8px',
                      borderRadius: '12px',
                      fontSize: '12px',
                      fontWeight: '500',
                      backgroundColor: client.isActive ? '#dcfce7' : '#f3f4f6',
                      color: client.isActive ? '#166534' : '#6b7280'
                    }}>
                      {client.isActive ? 'نشط' : 'غير نشط'}
                    </span>
                  </td>
                  <td style={{
                    padding: '16px 24px'
                  }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}>
                      <button
                        onClick={() => onView(client)}
                        style={{
                          padding: '4px',
                          color: '#6b7280',
                          background: 'none',
                          border: 'none',
                          borderRadius: '4px',
                          cursor: 'pointer'
                        }}
                        title="عرض التفاصيل"
                        onMouseOver={(e) => e.target.style.color = '#2563eb'}
                        onMouseOut={(e) => e.target.style.color = '#6b7280'}
                      >
                        👁️
                      </button>
                      <button
                        onClick={() => onEdit(client)}
                        style={{
                          padding: '4px',
                          color: '#6b7280',
                          background: 'none',
                          border: 'none',
                          borderRadius: '4px',
                          cursor: 'pointer'
                        }}
                        title="تعديل"
                        onMouseOver={(e) => e.target.style.color = '#10b981'}
                        onMouseOut={(e) => e.target.style.color = '#6b7280'}
                      >
                        ✏️
                      </button>
                      <button
                        onClick={() => onDelete(client)}
                        style={{
                          padding: '4px',
                          color: '#6b7280',
                          background: 'none',
                          border: 'none',
                          borderRadius: '4px',
                          cursor: 'pointer'
                        }}
                        title="حذف"
                        onMouseOver={(e) => e.target.style.color = '#ef4444'}
                        onMouseOut={(e) => e.target.style.color = '#6b7280'}
                      >
                        🗑️
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

// Search and Filters Component
interface SearchFiltersProps {
  filters: ClientFilters;
  onFiltersChange: (filters: ClientFilters) => void;
  onAddClient: () => void;
}

const SearchFilters = ({ filters, onFiltersChange, onAddClient }: SearchFiltersProps) => {
  const handleSearchChange = (search: string) => {
    onFiltersChange({ ...filters, search, page: 1 });
  };

  const handleStatusChange = (isActive: boolean | undefined) => {
    onFiltersChange({ ...filters, isActive, page: 1 });
  };

  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
      overflow: 'hidden'
    }}>
      <div style={{ padding: '24px' }}>
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '16px'
        }} className="sm:flex-row sm:items-center sm:justify-between">
          {/* Search */}
          <div style={{ flex: 1, maxWidth: '400px' }}>
            <div style={{ position: 'relative' }}>
              <span style={{
                position: 'absolute',
                right: '12px',
                top: '50%',
                transform: 'translateY(-50%)',
                fontSize: '16px',
                color: '#9ca3af'
              }}>
                🔍
              </span>
              <input
                type="text"
                placeholder="البحث في العملاء..."
                style={{
                  width: '100%',
                  padding: '12px 16px 12px 44px',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '16px',
                  outline: 'none'
                }}
                value={filters.search || ''}
                onChange={(e) => handleSearchChange(e.target.value)}
                onFocus={(e) => {
                  e.target.style.borderColor = '#3b82f6';
                  e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#d1d5db';
                  e.target.style.boxShadow = 'none';
                }}
              />
            </div>
          </div>

          {/* Filters */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '16px'
          }}>
            <select
              style={{
                padding: '12px 16px',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '14px',
                outline: 'none',
                backgroundColor: 'white'
              }}
              value={filters.isActive === undefined ? 'all' : filters.isActive.toString()}
              onChange={(e) => {
                const value = e.target.value;
                handleStatusChange(
                  value === 'all' ? undefined : value === 'true'
                );
              }}
            >
              <option value="all">جميع العملاء</option>
              <option value="true">العملاء النشطون</option>
              <option value="false">العملاء غير النشطين</option>
            </select>

            <button
              onClick={onAddClient}
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                padding: '12px 24px',
                backgroundColor: '#2563eb',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'background-color 0.2s'
              }}
              onMouseOver={(e) => e.target.style.backgroundColor = '#1d4ed8'}
              onMouseOut={(e) => e.target.style.backgroundColor = '#2563eb'}
            >
              <span style={{ marginLeft: '8px' }}>➕</span>
              إضافة عميل
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function ClientsPage() {
  const [filters, setFilters] = useState<ClientFilters>({
    page: 1,
    limit: 10,
    search: '',
    isActive: undefined,
  });

  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<any>(null);

  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);

  // Fetch clients
  useEffect(() => {
    const fetchClients = async () => {
      try {
        setLoading(true);
        const response = await apiClient.getClients(filters);
        if (response.success) {
          setClients(response.data || []);
          setPagination(response.pagination);
        } else {
          setError(response.message);
        }
      } catch (err: any) {
        setError(err.message || 'حدث خطأ في تحميل العملاء');
      } finally {
        setLoading(false);
      }
    };

    fetchClients();
  }, [filters]);

  const handleView = (client: Client) => {
    setSelectedClient(client);
    setShowViewModal(true);
  };

  const handleEdit = (client: Client) => {
    setSelectedClient(client);
    setShowEditModal(true);
  };

  const handleDelete = (client: Client) => {
    setSelectedClient(client);
    setShowDeleteModal(true);
  };

  const handleAddClient = () => {
    setSelectedClient(null);
    setShowAddModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedClient) return;

    try {
      await apiClient.deleteClient(selectedClient.id);
      // Refresh the list
      const response = await apiClient.getClients(filters);
      if (response.success) {
        setClients(response.data || []);
        setPagination(response.pagination);
      }
      setShowDeleteModal(false);
      setSelectedClient(null);
    } catch (error) {
      alert('حدث خطأ أثناء حذف العميل');
    }
  };

  const handleClientSave = async (clientData: Partial<Client>) => {
    try {
      if (selectedClient) {
        // Update existing client
        await apiClient.updateClient(selectedClient.id, clientData);
      } else {
        // Create new client
        await apiClient.createClient(clientData);
      }

      // Refresh the list
      const response = await apiClient.getClients(filters);
      if (response.success) {
        setClients(response.data || []);
        setPagination(response.pagination);
      }

      // Close modals
      setShowAddModal(false);
      setShowEditModal(false);
      setSelectedClient(null);
    } catch (error) {
      alert('حدث خطأ أثناء حفظ العميل');
    }
  };

  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page });
  };

  if (error) {
    return (
      <div style={{
        fontFamily: 'Cairo, Arial, sans-serif',
        direction: 'rtl'
      }}>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <h1 style={{
              fontSize: '28px',
              fontWeight: 'bold',
              color: '#111827',
              margin: 0
            }}>
              العملاء
            </h1>
          </div>
          
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
            overflow: 'hidden'
          }}>
            <div style={{ padding: '48px 24px', textAlign: 'center' }}>
              <div style={{ fontSize: '48px', color: '#ef4444', marginBottom: '16px' }}>⚠️</div>
              <h3 style={{
                fontSize: '18px',
                fontWeight: '500',
                color: '#111827',
                marginBottom: '8px'
              }}>
                خطأ في تحميل البيانات
              </h3>
              <p style={{
                color: '#6b7280',
                marginBottom: '16px'
              }}>
                {error}
              </p>
              <button
                onClick={() => window.location.reload()}
                style={{
                  padding: '12px 24px',
                  backgroundColor: '#2563eb',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}
              >
                إعادة المحاولة
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: 'rtl'
    }}>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <div>
            <h1 style={{
              fontSize: '28px',
              fontWeight: 'bold',
              color: '#111827',
              margin: '0 0 4px 0'
            }}>
              العملاء
            </h1>
            <p style={{
              color: '#6b7280',
              margin: 0
            }}>
              إدارة قائمة العملاء ومعلوماتهم
            </p>
          </div>
          <div style={{
            fontSize: '14px',
            color: '#6b7280'
          }}>
            {pagination && (
              <span>
                إجمالي العملاء: {pagination.total}
              </span>
            )}
          </div>
        </div>

        {/* Search and Filters */}
        <SearchFilters
          filters={filters}
          onFiltersChange={setFilters}
          onAddClient={handleAddClient}
        />

        {/* Clients Table */}
        <ClientsTable
          clients={clients}
          loading={loading}
          onView={handleView}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />

        {/* Pagination */}
        {pagination && pagination.totalPages > 1 && (
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
            overflow: 'hidden'
          }}>
            <div style={{ padding: '24px' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}>
                <div style={{
                  fontSize: '14px',
                  color: '#374151'
                }}>
                  عرض {((pagination.page - 1) * pagination.limit) + 1} إلى{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} من{' '}
                  {pagination.total} عميل
                </div>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <button
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page <= 1}
                    style={{
                      padding: '8px 16px',
                      backgroundColor: pagination.page <= 1 ? '#f3f4f6' : '#2563eb',
                      color: pagination.page <= 1 ? '#9ca3af' : 'white',
                      border: 'none',
                      borderRadius: '6px',
                      fontSize: '14px',
                      cursor: pagination.page <= 1 ? 'not-allowed' : 'pointer'
                    }}
                  >
                    السابق
                  </button>
                  <span style={{
                    fontSize: '14px',
                    color: '#374151'
                  }}>
                    صفحة {pagination.page} من {pagination.totalPages}
                  </span>
                  <button
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page >= pagination.totalPages}
                    style={{
                      padding: '8px 16px',
                      backgroundColor: pagination.page >= pagination.totalPages ? '#f3f4f6' : '#2563eb',
                      color: pagination.page >= pagination.totalPages ? '#9ca3af' : 'white',
                      border: 'none',
                      borderRadius: '6px',
                      fontSize: '14px',
                      cursor: pagination.page >= pagination.totalPages ? 'not-allowed' : 'pointer'
                    }}
                  >
                    التالي
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      {showAddModal && (
        <ClientModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          onSave={handleClientSave}
          title="إضافة عميل جديد"
        />
      )}

      {showEditModal && selectedClient && (
        <ClientModal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          onSave={handleClientSave}
          client={selectedClient}
          title="تعديل العميل"
        />
      )}

      {showViewModal && selectedClient && (
        <ClientViewModal
          isOpen={showViewModal}
          onClose={() => setShowViewModal(false)}
          client={selectedClient}
        />
      )}

      {showDeleteModal && selectedClient && (
        <DeleteConfirmModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={handleDeleteConfirm}
          title="حذف العميل"
          message={`هل أنت متأكد من حذف العميل "${selectedClient.name}"؟ هذا الإجراء لا يمكن التراجع عنه.`}
        />
      )}

      {/* Add CSS animation */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}
