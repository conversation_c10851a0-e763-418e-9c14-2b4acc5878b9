'use client';

import { useState } from 'react';

export default function TestAPIPage() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testAPI = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:4001/api/clients', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);
      
      const data = await response.json();
      setResult(JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('Error:', error);
      setResult(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testLogin = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:4001/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'admin123'
        }),
      });

      console.log('Login Response status:', response.status);

      const data = await response.json();
      setResult(JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('Login Error:', error);
      setResult(`Login Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testCreateClient = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:4001/api/clients', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'عميل تجريبي',
          email: '<EMAIL>',
          phone: '+966501111111',
          city: 'الرياض'
        }),
      });

      console.log('Create Client Response status:', response.status);

      const data = await response.json();
      setResult(JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('Create Client Error:', error);
      setResult(`Create Client Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: 'rtl',
      padding: '24px',
      maxWidth: '800px',
      margin: '0 auto'
    }}>
      <h1 style={{
        fontSize: '24px',
        fontWeight: 'bold',
        marginBottom: '24px'
      }}>
        اختبار API
      </h1>
      
      <div style={{
        display: 'flex',
        gap: '12px',
        marginBottom: '24px'
      }}>
        <button
          onClick={testAPI}
          disabled={loading}
          style={{
            padding: '12px 24px',
            backgroundColor: '#2563eb',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: loading ? 'not-allowed' : 'pointer',
            opacity: loading ? 0.5 : 1
          }}
        >
          {loading ? 'جاري الاختبار...' : 'اختبار API العملاء'}
        </button>
        
        <button
          onClick={testLogin}
          disabled={loading}
          style={{
            padding: '12px 24px',
            backgroundColor: '#16a34a',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: loading ? 'not-allowed' : 'pointer',
            opacity: loading ? 0.5 : 1
          }}
        >
          {loading ? 'جاري الاختبار...' : 'اختبار تسجيل الدخول'}
        </button>

        <button
          onClick={testCreateClient}
          disabled={loading}
          style={{
            padding: '12px 24px',
            backgroundColor: '#dc2626',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: loading ? 'not-allowed' : 'pointer',
            opacity: loading ? 0.5 : 1
          }}
        >
          {loading ? 'جاري الاختبار...' : 'اختبار إضافة عميل'}
        </button>
      </div>
      
      <div style={{
        backgroundColor: '#f3f4f6',
        padding: '16px',
        borderRadius: '8px',
        border: '1px solid #d1d5db',
        minHeight: '200px'
      }}>
        <h3 style={{
          fontSize: '16px',
          fontWeight: '600',
          marginBottom: '12px'
        }}>
          النتيجة:
        </h3>
        <pre style={{
          fontSize: '14px',
          lineHeight: '1.5',
          margin: 0,
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word'
        }}>
          {result || 'اضغط على أحد الأزرار لاختبار API'}
        </pre>
      </div>
      
      <div style={{
        marginTop: '24px',
        padding: '16px',
        backgroundColor: '#fef3c7',
        borderRadius: '8px',
        border: '1px solid #f59e0b'
      }}>
        <h4 style={{
          fontSize: '14px',
          fontWeight: '600',
          marginBottom: '8px',
          color: '#92400e'
        }}>
          معلومات مهمة:
        </h4>
        <ul style={{
          fontSize: '14px',
          color: '#92400e',
          margin: 0,
          paddingRight: '20px'
        }}>
          <li>تأكد من تشغيل XAMPP (Apache)</li>
          <li>تأكد من وجود الملفات في: C:\xampp\htdocs\backend-laravel\api\</li>
          <li>افتح Developer Tools (F12) لرؤية تفاصيل الأخطاء</li>
          <li>تحقق من Console للرسائل التفصيلية</li>
        </ul>
      </div>
    </div>
  );
}
