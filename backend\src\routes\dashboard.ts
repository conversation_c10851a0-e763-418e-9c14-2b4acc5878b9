import { Router } from 'express';
import { DashboardController } from '@/controllers/dashboardController';
import { authenticate, authorize } from '@/middleware/auth';

const router = Router();

// جميع المسارات تتطلب مصادقة
router.use(authenticate);

// الحصول على إحصائيات لوحة التحكم
router.get('/stats', 
  authorize(['dashboard:read']),
  DashboardController.getDashboardStats
);

// الحصول على إحصائيات المبيعات الشهرية
router.get('/sales/monthly', 
  authorize(['dashboard:read']),
  DashboardController.getMonthlySalesStats
);

// الحصول على إحصائيات حالة الفواتير
router.get('/invoices/status-stats', 
  authorize(['dashboard:read']),
  DashboardController.getInvoiceStatusStats
);

// الحصول على الفواتير المتأخرة
router.get('/invoices/overdue', 
  authorize(['invoices:read']),
  DashboardController.getOverdueInvoices
);

export default router;
