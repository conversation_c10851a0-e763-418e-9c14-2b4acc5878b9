@echo off
chcp 65001 >nul
title نظام إدارة الفواتير - Web Invoice SaaS

echo ========================================
echo      🚀 نظام إدارة الفواتير
echo        Web Invoice SaaS
echo ========================================
echo.

REM الحصول على مسار المشروع
set PROJECT_ROOT=%~dp0
cd /d "%PROJECT_ROOT%"

echo 📁 مسار المشروع: %CD%
echo.

echo ========================================
echo           فحص المتطلبات
echo ========================================
echo.

REM فحص Node.js
node --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js غير مثبت
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo ✅ Node.js مثبت: %NODE_VERSION%
)

REM فحص ملف قاعدة البيانات
if exist "database\web_invoice_saas_complete.sql" (
    echo ✅ ملف قاعدة البيانات موجود
) else (
    echo ❌ ملف قاعدة البيانات غير موجود
    echo يرجى التأكد من وجود: database\web_invoice_saas_complete.sql
    pause
    exit /b 1
)

REM فحص الخادم البسيط
if exist "backend\server-basic.js" (
    echo ✅ الخادم البسيط جاهز
) else (
    echo ❌ ملف الخادم غير موجود
    echo يرجى التأكد من وجود: backend\server-basic.js
    pause
    exit /b 1
)

REM فحص واجهة الاختبار
if exist "frontend\index.html" (
    echo ✅ واجهة الاختبار جاهزة
) else (
    echo ❌ واجهة الاختبار غير موجودة
    echo يرجى التأكد من وجود: frontend\index.html
    pause
    exit /b 1
)

echo.
echo ========================================
echo           تشغيل النظام
echo ========================================
echo.

echo 🚀 تشغيل خادم API...
cd backend
start cmd /k "title نظام إدارة الفواتير - API Server && echo 🚀 خادم API يعمل على المنفذ 5000 && echo. && echo للإيقاف: اضغط Ctrl+C && echo. && node server-basic.js"
cd ..

REM انتظار قليل لبدء الخادم
echo ⏳ انتظار بدء الخادم...
timeout /t 3 >nul

echo.
echo ========================================
echo            النظام جاهز!
echo ========================================
echo.

echo 🌐 الروابط المتاحة:
echo.
echo    📡 خادم API:
echo       الصحة: http://localhost:5000/health
echo       المعلومات: http://localhost:5000/api
echo       اختبار قاعدة البيانات: http://localhost:5000/api/test-db
echo.
echo    🔐 تسجيل الدخول:
echo       POST http://localhost:5000/api/auth/login
echo       البريد الإلكتروني: <EMAIL>
echo       كلمة المرور: admin123
echo.
echo    📊 البيانات:
echo       العملاء: http://localhost:5000/api/clients
echo       الفواتير: http://localhost:5000/api/invoices
echo       لوحة التحكم: http://localhost:5000/api/dashboard/stats
echo.
echo    🌐 واجهة الاختبار:
echo       ملف محلي: frontend\index.html
echo.
echo    🗄️ إدارة قاعدة البيانات:
echo       phpMyAdmin: http://localhost/phpmyadmin
echo       قاعدة البيانات: web_invoice_saas
echo       المستخدم: invoice_app
echo       كلمة المرور: invoice123
echo.

echo 📋 معلومات مهمة:
echo.
echo    🏢 المؤسسات التجريبية:
echo       1. شركة التقنية المتقدمة
echo       2. مؤسسة الابتكار الرقمي
echo.
echo    👥 المستخدمون التجريبيون:
echo       - <EMAIL> (مدير عام)
echo       - <EMAIL> (مستخدم عادي)
echo       - <EMAIL> (مدير)
echo.
echo    📄 الفواتير التجريبية:
echo       - INV-2024-001 (مرسلة - 11,500 ريال)
echo       - INV-2024-002 (مدفوعة - 8,250 ريال)
echo       - DIG-2024-001 (مسودة - 17,250 ريال)
echo.

echo 🔧 أوامر مفيدة:
echo.
echo    اختبار API:
echo       curl http://localhost:5000/health
echo.
echo    اختبار تسجيل الدخول:
echo       curl -X POST http://localhost:5000/api/auth/login ^
echo            -H "Content-Type: application/json" ^
echo            -d "{\"email\":\"<EMAIL>\",\"password\":\"admin123\"}"
echo.
echo    فحص قاعدة البيانات:
echo       node scripts\test-xampp-connection.js
echo.

REM فتح المتصفح (اختياري)
set /p OPEN_BROWSER="هل تريد فتح واجهة الاختبار في المتصفح؟ (y/n): "
if /i "%OPEN_BROWSER%"=="y" (
    echo 🌐 فتح واجهة الاختبار...
    
    REM فتح واجهة الاختبار
    start "" "%PROJECT_ROOT%frontend\index.html"
    
    REM فتح API info
    timeout /t 2 >nul
    start http://localhost:5000/api
    
    REM فتح phpMyAdmin (إذا كان XAMPP يعمل)
    timeout /t 1 >nul
    start http://localhost/phpmyadmin
)

echo.
echo ✨ النظام يعمل بنجاح!
echo.
echo 💡 نصائح:
echo    - استخدم واجهة الاختبار لتجربة جميع الميزات
echo    - تحقق من phpMyAdmin لرؤية البيانات
echo    - راجع الوثائق في مجلد docs للمزيد من المعلومات
echo.
echo 🛑 لإيقاف النظام:
echo    - أغلق نافذة خادم API أو اضغط Ctrl+C
echo.

echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
