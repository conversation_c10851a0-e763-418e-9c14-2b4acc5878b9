'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import {
  CogIcon,
  BuildingOfficeIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  UserIcon,
  BellIcon,
  ShieldCheckIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '@/store/auth';

// Settings Tab Component
interface SettingsTabProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const SettingsTabs = ({ activeTab, onTabChange }: SettingsTabProps) => {
  const tabs = [
    { id: 'company', label: 'معلومات الشركة', icon: BuildingOfficeIcon },
    { id: 'invoice', label: 'إعدادات الفواتير', icon: DocumentTextIcon },
    { id: 'payment', label: 'طرق الدفع', icon: CurrencyDollarIcon },
    { id: 'profile', label: 'الملف الشخصي', icon: UserIcon },
    { id: 'notifications', label: 'الإشعارات', icon: BellIcon },
    { id: 'security', label: 'الأمان', icon: ShieldCheckIcon },
  ];

  return (
    <div className="card">
      <div className="card-body p-0">
        <nav className="flex flex-col space-y-1 p-4">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => onTabChange(tab.id)}
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-700'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <Icon className={`me-3 h-5 w-5 ${
                  activeTab === tab.id ? 'text-blue-500' : 'text-gray-400'
                }`} />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>
    </div>
  );
};

// Company Settings Component
const CompanySettings = () => {
  const { tenant } = useAuth();
  const { register, handleSubmit, formState: { errors } } = useForm({
    defaultValues: {
      companyName: tenant?.name || '',
      companyAddress: '',
      companyPhone: '',
      companyEmail: '',
      companyWebsite: '',
      taxNumber: '',
    },
  });

  const onSubmit = (data: any) => {
    console.log('Company settings:', data);
    // TODO: Save company settings
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold text-gray-900">معلومات الشركة</h3>
        <p className="text-sm text-gray-600">
          تحديث معلومات شركتك التي ستظهر في الفواتير
        </p>
      </div>
      <div className="card-body">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="form-label">اسم الشركة</label>
              <input
                type="text"
                className="form-input"
                {...register('companyName', { required: 'اسم الشركة مطلوب' })}
              />
              {errors.companyName && (
                <p className="form-error">{errors.companyName.message}</p>
              )}
            </div>

            <div>
              <label className="form-label">الرقم الضريبي</label>
              <input
                type="text"
                className="form-input"
                {...register('taxNumber')}
              />
            </div>

            <div className="md:col-span-2">
              <label className="form-label">عنوان الشركة</label>
              <textarea
                className="form-input"
                rows={3}
                {...register('companyAddress')}
              />
            </div>

            <div>
              <label className="form-label">رقم الهاتف</label>
              <input
                type="tel"
                className="form-input"
                {...register('companyPhone')}
              />
            </div>

            <div>
              <label className="form-label">البريد الإلكتروني</label>
              <input
                type="email"
                className="form-input"
                {...register('companyEmail')}
              />
            </div>

            <div className="md:col-span-2">
              <label className="form-label">موقع الشركة</label>
              <input
                type="url"
                className="form-input"
                {...register('companyWebsite')}
              />
            </div>
          </div>

          <div className="flex justify-end">
            <button type="submit" className="btn btn-primary">
              حفظ التغييرات
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Invoice Settings Component
const InvoiceSettings = () => {
  const { register, handleSubmit, formState: { errors } } = useForm({
    defaultValues: {
      invoicePrefix: 'INV',
      invoiceNumbering: 'SEQUENTIAL',
      nextInvoiceNumber: 1,
      defaultDueDays: 30,
      defaultCurrency: 'SAR',
      defaultTaxRate: 0.15,
      invoiceTerms: '',
      invoiceFooter: '',
    },
  });

  const onSubmit = (data: any) => {
    console.log('Invoice settings:', data);
    // TODO: Save invoice settings
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold text-gray-900">إعدادات الفواتير</h3>
        <p className="text-sm text-gray-600">
          تخصيص شكل وسلوك الفواتير
        </p>
      </div>
      <div className="card-body">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="form-label">بادئة رقم الفاتورة</label>
              <input
                type="text"
                className="form-input"
                {...register('invoicePrefix', { required: 'البادئة مطلوبة' })}
              />
              {errors.invoicePrefix && (
                <p className="form-error">{errors.invoicePrefix.message}</p>
              )}
            </div>

            <div>
              <label className="form-label">نظام الترقيم</label>
              <select className="form-input" {...register('invoiceNumbering')}>
                <option value="SEQUENTIAL">تسلسلي</option>
                <option value="RANDOM">عشوائي</option>
                <option value="CUSTOM">مخصص</option>
              </select>
            </div>

            <div>
              <label className="form-label">الرقم التالي</label>
              <input
                type="number"
                className="form-input"
                {...register('nextInvoiceNumber', { 
                  required: 'الرقم التالي مطلوب',
                  min: { value: 1, message: 'يجب أن يكون الرقم أكبر من 0' }
                })}
              />
              {errors.nextInvoiceNumber && (
                <p className="form-error">{errors.nextInvoiceNumber.message}</p>
              )}
            </div>

            <div>
              <label className="form-label">أيام الاستحقاق الافتراضية</label>
              <input
                type="number"
                className="form-input"
                {...register('defaultDueDays', { 
                  required: 'أيام الاستحقاق مطلوبة',
                  min: { value: 1, message: 'يجب أن تكون أكبر من 0' }
                })}
              />
              {errors.defaultDueDays && (
                <p className="form-error">{errors.defaultDueDays.message}</p>
              )}
            </div>

            <div>
              <label className="form-label">العملة الافتراضية</label>
              <select className="form-input" {...register('defaultCurrency')}>
                <option value="SAR">ريال سعودي (SAR)</option>
                <option value="USD">دولار أمريكي (USD)</option>
                <option value="EUR">يورو (EUR)</option>
                <option value="AED">درهم إماراتي (AED)</option>
              </select>
            </div>

            <div>
              <label className="form-label">معدل الضريبة الافتراضي (%)</label>
              <input
                type="number"
                step="0.01"
                min="0"
                max="100"
                className="form-input"
                {...register('defaultTaxRate', { 
                  required: 'معدل الضريبة مطلوب',
                  min: { value: 0, message: 'يجب أن يكون 0 أو أكثر' },
                  max: { value: 1, message: 'يجب أن يكون 1 أو أقل' }
                })}
              />
              {errors.defaultTaxRate && (
                <p className="form-error">{errors.defaultTaxRate.message}</p>
              )}
            </div>

            <div className="md:col-span-2">
              <label className="form-label">شروط وأحكام الفاتورة</label>
              <textarea
                className="form-input"
                rows={4}
                placeholder="أدخل الشروط والأحكام الافتراضية للفواتير..."
                {...register('invoiceTerms')}
              />
            </div>

            <div className="md:col-span-2">
              <label className="form-label">تذييل الفاتورة</label>
              <textarea
                className="form-input"
                rows={3}
                placeholder="نص يظهر في أسفل كل فاتورة..."
                {...register('invoiceFooter')}
              />
            </div>
          </div>

          <div className="flex justify-end">
            <button type="submit" className="btn btn-primary">
              حفظ التغييرات
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Profile Settings Component
const ProfileSettings = () => {
  const { user } = useAuth();
  const { register, handleSubmit, formState: { errors } } = useForm({
    defaultValues: {
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      email: user?.email || '',
      phone: user?.phone || '',
    },
  });

  const onSubmit = (data: any) => {
    console.log('Profile settings:', data);
    // TODO: Save profile settings
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold text-gray-900">الملف الشخصي</h3>
        <p className="text-sm text-gray-600">
          تحديث معلوماتك الشخصية
        </p>
      </div>
      <div className="card-body">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="form-label">الاسم الأول</label>
              <input
                type="text"
                className="form-input"
                {...register('firstName', { required: 'الاسم الأول مطلوب' })}
              />
              {errors.firstName && (
                <p className="form-error">{errors.firstName.message}</p>
              )}
            </div>

            <div>
              <label className="form-label">الاسم الأخير</label>
              <input
                type="text"
                className="form-input"
                {...register('lastName', { required: 'الاسم الأخير مطلوب' })}
              />
              {errors.lastName && (
                <p className="form-error">{errors.lastName.message}</p>
              )}
            </div>

            <div>
              <label className="form-label">البريد الإلكتروني</label>
              <input
                type="email"
                className="form-input"
                {...register('email', { 
                  required: 'البريد الإلكتروني مطلوب',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'صيغة البريد الإلكتروني غير صحيحة'
                  }
                })}
              />
              {errors.email && (
                <p className="form-error">{errors.email.message}</p>
              )}
            </div>

            <div>
              <label className="form-label">رقم الهاتف</label>
              <input
                type="tel"
                className="form-input"
                {...register('phone')}
              />
            </div>
          </div>

          <div className="flex justify-end">
            <button type="submit" className="btn btn-primary">
              حفظ التغييرات
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Simple Settings Component for other tabs
const SimpleSettings = ({ title, description }: { title: string; description: string }) => {
  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
      <div className="card-body">
        <div className="text-center py-12">
          <CogIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            قريباً
          </h3>
          <p className="text-gray-500">
            هذا القسم قيد التطوير وسيكون متاحاً قريباً
          </p>
        </div>
      </div>
    </div>
  );
};

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('company');

  const renderTabContent = () => {
    switch (activeTab) {
      case 'company':
        return <CompanySettings />;
      case 'invoice':
        return <InvoiceSettings />;
      case 'profile':
        return <ProfileSettings />;
      case 'payment':
        return <SimpleSettings title="طرق الدفع" description="إعداد طرق الدفع المقبولة" />;
      case 'notifications':
        return <SimpleSettings title="الإشعارات" description="إدارة إعدادات الإشعارات" />;
      case 'security':
        return <SimpleSettings title="الأمان" description="إعدادات الأمان وكلمة المرور" />;
      default:
        return <CompanySettings />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">الإعدادات</h1>
        <p className="text-gray-600">
          إدارة إعدادات النظام والحساب
        </p>
      </div>

      {/* Settings Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <SettingsTabs activeTab={activeTab} onTabChange={setActiveTab} />
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
}
