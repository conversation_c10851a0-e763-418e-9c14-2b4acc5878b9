'use client';

import { useEffect, useState } from 'react';

export default function TestPage() {
  const [status, setStatus] = useState('جاري التحقق...');
  const [apiData, setApiData] = useState<any>(null);
  const [loginResult, setLoginResult] = useState<any>(null);

  useEffect(() => {
    // Test API health
    fetch('http://localhost:5001/health')
      .then(res => res.json())
      .then(data => {
        setStatus('✅ API متصل');
        setApiData(data);
      })
      .catch(err => {
        setStatus('❌ API غير متصل');
        console.error('API Error:', err);
      });
  }, []);

  const testLogin = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'admin123'
        })
      });
      
      const data = await response.json();
      setLoginResult(data);
    } catch (error) {
      setLoginResult({ error: error.toString() });
    }
  };

  const testClients = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/clients');
      const data = await response.json();
      alert('العملاء: ' + JSON.stringify(data, null, 2));
    } catch (error) {
      alert('خطأ: ' + error);
    }
  };

  const testInvoices = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/invoices');
      const data = await response.json();
      alert('الفواتير: ' + JSON.stringify(data, null, 2));
    } catch (error) {
      alert('خطأ: ' + error);
    }
  };

  const testStats = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/dashboard/stats');
      const data = await response.json();
      alert('الإحصائيات: ' + JSON.stringify(data, null, 2));
    } catch (error) {
      alert('خطأ: ' + error);
    }
  };

  return (
    <div style={{ 
      fontFamily: 'Cairo, Arial, sans-serif', 
      padding: '20px', 
      direction: 'rtl',
      backgroundColor: '#f0f9ff',
      minHeight: '100vh'
    }}>
      <div style={{
        maxWidth: '900px',
        margin: '0 auto',
        backgroundColor: 'white',
        padding: '30px',
        borderRadius: '12px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.1)'
      }}>
        <h1 style={{ 
          color: '#1e40af', 
          textAlign: 'center',
          marginBottom: '30px',
          fontSize: '2.5rem',
          fontWeight: 'bold'
        }}>
          🧪 صفحة اختبار النظام
        </h1>
        
        <div style={{
          backgroundColor: '#f1f5f9',
          padding: '20px',
          borderRadius: '8px',
          marginBottom: '25px',
          border: '1px solid #e2e8f0'
        }}>
          <h2 style={{ color: '#374151', marginBottom: '15px', fontSize: '1.5rem' }}>حالة النظام</h2>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
            <div>
              <strong>Backend API:</strong> {status}
            </div>
            <div>
              <strong>Frontend:</strong> ✅ يعمل
            </div>
            <div>
              <strong>المنفذ الأمامي:</strong> http://localhost:3000
            </div>
            <div>
              <strong>منفذ API:</strong> http://localhost:5001
            </div>
          </div>
        </div>

        {apiData && (
          <div style={{
            backgroundColor: '#ecfdf5',
            padding: '20px',
            borderRadius: '8px',
            marginBottom: '25px',
            border: '1px solid #d1fae5'
          }}>
            <h3 style={{ color: '#065f46', marginBottom: '15px' }}>معلومات API</h3>
            <div style={{ fontSize: '14px', color: '#374151' }}>
              <p><strong>الرسالة:</strong> {apiData.message}</p>
              <p><strong>الإصدار:</strong> {apiData.version}</p>
              <p><strong>البيئة:</strong> {apiData.environment}</p>
              <p><strong>الوقت:</strong> {new Date(apiData.timestamp).toLocaleString('ar-SA')}</p>
            </div>
          </div>
        )}

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '15px',
          marginBottom: '30px'
        }}>
          <button
            onClick={testLogin}
            style={{
              backgroundColor: '#2563eb',
              color: 'white',
              padding: '15px 20px',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              transition: 'all 0.2s'
            }}
            onMouseOver={(e) => e.target.style.backgroundColor = '#1d4ed8'}
            onMouseOut={(e) => e.target.style.backgroundColor = '#2563eb'}
          >
            🔐 اختبار تسجيل الدخول
          </button>
          
          <button
            onClick={testClients}
            style={{
              backgroundColor: '#059669',
              color: 'white',
              padding: '15px 20px',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500'
            }}
          >
            👥 اختبار العملاء
          </button>
          
          <button
            onClick={testInvoices}
            style={{
              backgroundColor: '#dc2626',
              color: 'white',
              padding: '15px 20px',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500'
            }}
          >
            📄 اختبار الفواتير
          </button>
          
          <button
            onClick={testStats}
            style={{
              backgroundColor: '#7c3aed',
              color: 'white',
              padding: '15px 20px',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500'
            }}
          >
            📊 اختبار الإحصائيات
          </button>
        </div>

        {loginResult && (
          <div style={{
            backgroundColor: loginResult.success ? '#ecfdf5' : '#fef2f2',
            padding: '20px',
            borderRadius: '8px',
            marginBottom: '25px',
            border: `1px solid ${loginResult.success ? '#d1fae5' : '#fecaca'}`
          }}>
            <h3 style={{ 
              color: loginResult.success ? '#065f46' : '#dc2626', 
              marginBottom: '15px' 
            }}>
              نتيجة تسجيل الدخول
            </h3>
            <pre style={{ 
              backgroundColor: 'white', 
              padding: '15px', 
              borderRadius: '6px',
              fontSize: '12px',
              overflow: 'auto',
              border: '1px solid #e5e7eb'
            }}>
              {JSON.stringify(loginResult, null, 2)}
            </pre>
          </div>
        )}

        <div style={{
          backgroundColor: '#fef3c7',
          padding: '20px',
          borderRadius: '8px',
          marginBottom: '25px',
          border: '1px solid #fde68a'
        }}>
          <h3 style={{ color: '#92400e', marginBottom: '15px' }}>الحسابات التجريبية</h3>
          
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
            <div style={{ backgroundColor: 'white', padding: '15px', borderRadius: '6px' }}>
              <h4 style={{ color: '#374151', marginBottom: '8px', fontSize: '16px' }}>مدير عام</h4>
              <p style={{ margin: '0', fontSize: '14px', lineHeight: '1.5' }}>
                <strong>البريد:</strong> <EMAIL><br/>
                <strong>كلمة المرور:</strong> admin123<br/>
                <strong>الدور:</strong> SUPER_ADMIN
              </p>
            </div>
            
            <div style={{ backgroundColor: 'white', padding: '15px', borderRadius: '6px' }}>
              <h4 style={{ color: '#374151', marginBottom: '8px', fontSize: '16px' }}>مستخدم عادي</h4>
              <p style={{ margin: '0', fontSize: '14px', lineHeight: '1.5' }}>
                <strong>البريد:</strong> <EMAIL><br/>
                <strong>كلمة المرور:</strong> admin123<br/>
                <strong>الدور:</strong> USER
              </p>
            </div>
            
            <div style={{ backgroundColor: 'white', padding: '15px', borderRadius: '6px' }}>
              <h4 style={{ color: '#374151', marginBottom: '8px', fontSize: '16px' }}>مدير</h4>
              <p style={{ margin: '0', fontSize: '14px', lineHeight: '1.5' }}>
                <strong>البريد:</strong> <EMAIL><br/>
                <strong>كلمة المرور:</strong> admin123<br/>
                <strong>الدور:</strong> ADMIN
              </p>
            </div>
          </div>
        </div>

        <div style={{
          backgroundColor: '#e0f2fe',
          padding: '20px',
          borderRadius: '8px',
          textAlign: 'center'
        }}>
          <h3 style={{ color: '#0277bd', marginBottom: '15px' }}>روابط مفيدة</h3>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px', justifyContent: 'center' }}>
            <a href="http://localhost:5001/api" target="_blank" style={{ 
              color: '#0277bd', 
              textDecoration: 'none',
              padding: '8px 16px',
              backgroundColor: 'white',
              borderRadius: '6px',
              fontSize: '14px'
            }}>
              📡 API Info
            </a>
            <a href="http://localhost:5001/health" target="_blank" style={{ 
              color: '#0277bd', 
              textDecoration: 'none',
              padding: '8px 16px',
              backgroundColor: 'white',
              borderRadius: '6px',
              fontSize: '14px'
            }}>
              ❤️ Health Check
            </a>
            <a href="http://localhost/phpmyadmin" target="_blank" style={{ 
              color: '#0277bd', 
              textDecoration: 'none',
              padding: '8px 16px',
              backgroundColor: 'white',
              borderRadius: '6px',
              fontSize: '14px'
            }}>
              🗄️ Database
            </a>
            <a href="file:///D:/web_invoice/frontend/index.html" target="_blank" style={{ 
              color: '#0277bd', 
              textDecoration: 'none',
              padding: '8px 16px',
              backgroundColor: 'white',
              borderRadius: '6px',
              fontSize: '14px'
            }}>
              🧪 Test Interface
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
