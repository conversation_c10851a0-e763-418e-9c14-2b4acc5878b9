<?php
require_once '../config/database.php';

setCorsHeaders();

class ClientsAPI {
    private $db;
    private $tenant_id;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        
        // For demo purposes, we'll use tenant_id = 1
        // In production, this should come from JW<PERSON> token
        $this->tenant_id = 1;
    }

    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $path_parts = explode('/', trim($path, '/'));
        
        switch ($method) {
            case 'GET':
                if (isset($path_parts[2]) && is_numeric($path_parts[2])) {
                    $this->getClient($path_parts[2]);
                } else {
                    $this->getClients();
                }
                break;
            case 'POST':
                $this->createClient();
                break;
            case 'PUT':
                if (isset($path_parts[2]) && is_numeric($path_parts[2])) {
                    $this->updateClient($path_parts[2]);
                } else {
                    errorResponse('معرف العميل مطلوب', 400);
                }
                break;
            case 'DELETE':
                if (isset($path_parts[2]) && is_numeric($path_parts[2])) {
                    $this->deleteClient($path_parts[2]);
                } else {
                    errorResponse('معرف العميل مطلوب', 400);
                }
                break;
            default:
                errorResponse('طريقة غير مدعومة', 405);
        }
    }

    public function getClients() {
        try {
            $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
            $search = isset($_GET['search']) ? trim($_GET['search']) : '';
            $is_active = isset($_GET['isActive']) ? $_GET['isActive'] : null;
            
            $offset = ($page - 1) * $limit;
            
            // Build WHERE clause
            $where_conditions = ['tenant_id = :tenant_id'];
            $params = [':tenant_id' => $this->tenant_id];
            
            if (!empty($search)) {
                $where_conditions[] = '(name LIKE :search OR email LIKE :search OR phone LIKE :search)';
                $params[':search'] = "%$search%";
            }
            
            if ($is_active !== null) {
                $where_conditions[] = 'is_active = :is_active';
                $params[':is_active'] = $is_active === 'true' ? 1 : 0;
            }
            
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
            
            // Get total count
            $count_sql = "SELECT COUNT(*) as total FROM clients $where_clause";
            $count_stmt = $this->db->prepare($count_sql);
            $count_stmt->execute($params);
            $total = $count_stmt->fetch()['total'];
            
            // Get clients with statistics
            $sql = "SELECT 
                        c.*,
                        COALESCE(cs.total_invoices, 0) as total_invoices,
                        COALESCE(cs.paid_amount, 0) as paid_amount,
                        COALESCE(cs.pending_amount, 0) as pending_amount,
                        COALESCE(cs.total_amount, 0) as total_amount
                    FROM clients c
                    LEFT JOIN clients_with_stats cs ON c.id = cs.id
                    $where_clause
                    ORDER BY c.created_at DESC
                    LIMIT :limit OFFSET :offset";
            
            $stmt = $this->db->prepare($sql);
            
            // Bind parameters
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            
            $stmt->execute();
            $clients = $stmt->fetchAll();
            
            // Format clients data
            $formatted_clients = array_map(function($client) {
                return [
                    'id' => (string)$client['id'],
                    'name' => $client['name'],
                    'email' => $client['email'],
                    'phone' => $client['phone'],
                    'address' => $client['address'],
                    'city' => $client['city'],
                    'country' => $client['country'],
                    'taxNumber' => $client['tax_number'],
                    'isActive' => (bool)$client['is_active'],
                    'totalInvoices' => (int)$client['total_invoices'],
                    'paidAmount' => (float)$client['paid_amount'],
                    'pendingAmount' => (float)$client['pending_amount'],
                    'totalAmount' => (float)$client['total_amount'],
                    'createdAt' => $client['created_at'],
                    'updatedAt' => $client['updated_at']
                ];
            }, $clients);
            
            $pagination = [
                'page' => $page,
                'limit' => $limit,
                'total' => (int)$total,
                'totalPages' => ceil($total / $limit)
            ];
            
            successResponse($formatted_clients, 'تم جلب العملاء بنجاح', $pagination);
            
        } catch (Exception $e) {
            errorResponse('خطأ في جلب العملاء: ' . $e->getMessage(), 500);
        }
    }

    public function getClient($id) {
        try {
            $sql = "SELECT 
                        c.*,
                        COALESCE(cs.total_invoices, 0) as total_invoices,
                        COALESCE(cs.paid_amount, 0) as paid_amount,
                        COALESCE(cs.pending_amount, 0) as pending_amount,
                        COALESCE(cs.total_amount, 0) as total_amount
                    FROM clients c
                    LEFT JOIN clients_with_stats cs ON c.id = cs.id
                    WHERE c.id = :id AND c.tenant_id = :tenant_id";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->bindParam(':tenant_id', $this->tenant_id, PDO::PARAM_INT);
            $stmt->execute();
            
            $client = $stmt->fetch();
            
            if (!$client) {
                errorResponse('العميل غير موجود', 404);
            }
            
            $formatted_client = [
                'id' => (string)$client['id'],
                'name' => $client['name'],
                'email' => $client['email'],
                'phone' => $client['phone'],
                'address' => $client['address'],
                'city' => $client['city'],
                'country' => $client['country'],
                'taxNumber' => $client['tax_number'],
                'isActive' => (bool)$client['is_active'],
                'totalInvoices' => (int)$client['total_invoices'],
                'paidAmount' => (float)$client['paid_amount'],
                'pendingAmount' => (float)$client['pending_amount'],
                'totalAmount' => (float)$client['total_amount'],
                'createdAt' => $client['created_at'],
                'updatedAt' => $client['updated_at']
            ];
            
            successResponse($formatted_client, 'تم جلب العميل بنجاح');
            
        } catch (Exception $e) {
            errorResponse('خطأ في جلب العميل: ' . $e->getMessage(), 500);
        }
    }

    public function createClient() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            // Validate required fields
            if (empty($input['name'])) {
                errorResponse('اسم العميل مطلوب', 400);
            }
            
            // Validate email format if provided
            if (!empty($input['email']) && !filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
                errorResponse('البريد الإلكتروني غير صحيح', 400);
            }
            
            // Check if email already exists for this tenant
            if (!empty($input['email'])) {
                $check_sql = "SELECT id FROM clients WHERE email = :email AND tenant_id = :tenant_id";
                $check_stmt = $this->db->prepare($check_sql);
                $check_stmt->bindParam(':email', $input['email']);
                $check_stmt->bindParam(':tenant_id', $this->tenant_id);
                $check_stmt->execute();
                
                if ($check_stmt->fetch()) {
                    errorResponse('البريد الإلكتروني مستخدم بالفعل', 400);
                }
            }
            
            $sql = "INSERT INTO clients (tenant_id, name, email, phone, address, city, country, tax_number, is_active) 
                    VALUES (:tenant_id, :name, :email, :phone, :address, :city, :country, :tax_number, :is_active)";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':tenant_id', $this->tenant_id);
            $stmt->bindParam(':name', $input['name']);
            $stmt->bindParam(':email', $input['email']);
            $stmt->bindParam(':phone', $input['phone']);
            $stmt->bindParam(':address', $input['address']);
            $stmt->bindParam(':city', $input['city']);
            $stmt->bindParam(':country', $input['country'] ?? 'المملكة العربية السعودية');
            $stmt->bindParam(':tax_number', $input['taxNumber']);
            $stmt->bindValue(':is_active', isset($input['isActive']) ? ($input['isActive'] ? 1 : 0) : 1);
            
            $stmt->execute();
            $client_id = $this->db->lastInsertId();
            
            // Get the created client
            $this->getClient($client_id);
            
        } catch (Exception $e) {
            errorResponse('خطأ في إضافة العميل: ' . $e->getMessage(), 500);
        }
    }

    public function updateClient($id) {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            // Check if client exists
            $check_sql = "SELECT id FROM clients WHERE id = :id AND tenant_id = :tenant_id";
            $check_stmt = $this->db->prepare($check_sql);
            $check_stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $check_stmt->bindParam(':tenant_id', $this->tenant_id);
            $check_stmt->execute();
            
            if (!$check_stmt->fetch()) {
                errorResponse('العميل غير موجود', 404);
            }
            
            // Validate required fields
            if (empty($input['name'])) {
                errorResponse('اسم العميل مطلوب', 400);
            }
            
            // Validate email format if provided
            if (!empty($input['email']) && !filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
                errorResponse('البريد الإلكتروني غير صحيح', 400);
            }
            
            // Check if email already exists for another client
            if (!empty($input['email'])) {
                $check_sql = "SELECT id FROM clients WHERE email = :email AND tenant_id = :tenant_id AND id != :id";
                $check_stmt = $this->db->prepare($check_sql);
                $check_stmt->bindParam(':email', $input['email']);
                $check_stmt->bindParam(':tenant_id', $this->tenant_id);
                $check_stmt->bindParam(':id', $id, PDO::PARAM_INT);
                $check_stmt->execute();
                
                if ($check_stmt->fetch()) {
                    errorResponse('البريد الإلكتروني مستخدم بالفعل', 400);
                }
            }
            
            $sql = "UPDATE clients SET 
                        name = :name, 
                        email = :email, 
                        phone = :phone, 
                        address = :address, 
                        city = :city, 
                        country = :country, 
                        tax_number = :tax_number, 
                        is_active = :is_active,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = :id AND tenant_id = :tenant_id";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->bindParam(':tenant_id', $this->tenant_id);
            $stmt->bindParam(':name', $input['name']);
            $stmt->bindParam(':email', $input['email']);
            $stmt->bindParam(':phone', $input['phone']);
            $stmt->bindParam(':address', $input['address']);
            $stmt->bindParam(':city', $input['city']);
            $stmt->bindParam(':country', $input['country'] ?? 'المملكة العربية السعودية');
            $stmt->bindParam(':tax_number', $input['taxNumber']);
            $stmt->bindValue(':is_active', isset($input['isActive']) ? ($input['isActive'] ? 1 : 0) : 1);
            
            $stmt->execute();
            
            // Get the updated client
            $this->getClient($id);
            
        } catch (Exception $e) {
            errorResponse('خطأ في تحديث العميل: ' . $e->getMessage(), 500);
        }
    }

    public function deleteClient($id) {
        try {
            // Check if client exists
            $check_sql = "SELECT id FROM clients WHERE id = :id AND tenant_id = :tenant_id";
            $check_stmt = $this->db->prepare($check_sql);
            $check_stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $check_stmt->bindParam(':tenant_id', $this->tenant_id);
            $check_stmt->execute();
            
            if (!$check_stmt->fetch()) {
                errorResponse('العميل غير موجود', 404);
            }
            
            // Check if client has invoices
            $invoice_check_sql = "SELECT COUNT(*) as count FROM invoices WHERE client_id = :id";
            $invoice_check_stmt = $this->db->prepare($invoice_check_sql);
            $invoice_check_stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $invoice_check_stmt->execute();
            $invoice_count = $invoice_check_stmt->fetch()['count'];
            
            if ($invoice_count > 0) {
                errorResponse('لا يمكن حذف العميل لأنه مرتبط بفواتير موجودة', 400);
            }
            
            $sql = "DELETE FROM clients WHERE id = :id AND tenant_id = :tenant_id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->bindParam(':tenant_id', $this->tenant_id);
            $stmt->execute();
            
            successResponse(null, 'تم حذف العميل بنجاح');
            
        } catch (Exception $e) {
            errorResponse('خطأ في حذف العميل: ' . $e->getMessage(), 500);
        }
    }
}

// Handle the request
try {
    $api = new ClientsAPI();
    $api->handleRequest();
} catch (Exception $e) {
    errorResponse('خطأ في الخادم: ' . $e->getMessage(), 500);
}
?>
