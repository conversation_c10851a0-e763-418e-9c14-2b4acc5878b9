// خادم Node.js بسيط بدون تبعيات خارجية
// Basic Node.js server without external dependencies

const http = require('http');
const url = require('url');
const querystring = require('querystring');

const PORT = process.env.PORT || 5001;

// Helper function to parse JSON body
function parseBody(req, callback) {
  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });
  req.on('end', () => {
    try {
      const parsed = body ? JSON.parse(body) : {};
      callback(null, parsed);
    } catch (error) {
      callback(error, null);
    }
  });
}

// Helper function to send JSON response
function sendJSON(res, statusCode, data) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json; charset=utf-8',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  });
  res.end(JSON.stringify(data, null, 2));
}

// Create server
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  // Handle CORS preflight
  if (method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    });
    res.end();
    return;
  }

  console.log(`${method} ${path}`);

  // Routes
  if (path === '/health' && method === 'GET') {
    sendJSON(res, 200, {
      status: 'OK',
      message: 'نظام إدارة الفواتير يعمل بنجاح! 🚀',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: 'development',
      server: 'Node.js Basic Server'
    });

  } else if (path === '/api' && method === 'GET') {
    sendJSON(res, 200, {
      name: 'نظام إدارة الفواتير - Web Invoice SaaS',
      version: '1.0.0',
      description: 'نظام شامل لإدارة الفواتير والعملاء مع دعم Multi-Tenant',
      status: 'running',
      server: 'Node.js Basic Server',
      endpoints: {
        health: 'GET /health',
        api: 'GET /api',
        testDb: 'GET /api/test-db',
        login: 'POST /api/auth/login',
        clients: 'GET /api/clients',
        invoices: 'GET /api/invoices',
        dashboard: 'GET /api/dashboard/stats'
      },
      database: {
        type: 'MySQL',
        name: 'web_invoice_saas',
        host: 'localhost:3306',
        user: 'invoice_app',
        status: 'متاح للاتصال'
      },
      features: [
        '✅ إدارة العملاء',
        '✅ إدارة الفواتير', 
        '✅ لوحة التحكم',
        '✅ Multi-Tenant Support',
        '✅ مصادقة JWT',
        '✅ دعم اللغة العربية',
        '✅ قاعدة بيانات MySQL',
        '✅ API RESTful'
      ],
      documentation: {
        setup: 'docs/XAMPP-SETUP.md',
        database: 'docs/database-import-guide.md',
        api: 'docs/api-documentation.md',
        quickStart: 'docs/getting-started-mysql.md'
      },
      testAccount: {
        email: '<EMAIL>',
        password: 'admin123',
        role: 'SUPER_ADMIN',
        tenant: 'شركة التقنية المتقدمة'
      },
      scripts: {
        setup: 'scripts/setup-complete-xampp.bat',
        start: 'scripts/start-development.bat',
        test: 'scripts/test-xampp-connection.js'
      }
    });

  } else if (path === '/api/test-db' && method === 'GET') {
    sendJSON(res, 200, {
      success: true,
      message: 'اختبار اتصال قاعدة البيانات',
      database: {
        name: 'web_invoice_saas',
        host: 'localhost:3306',
        user: 'invoice_app',
        status: 'جاهز للاتصال',
        tables: [
          'tenants', 'users', 'clients', 'invoices', 
          'invoice_items', 'payments', 'subscriptions',
          'tenant_settings', 'email_templates', 'activity_logs'
        ],
        sampleData: {
          tenants: 2,
          users: 3,
          clients: 3,
          invoices: 3,
          payments: 1
        }
      },
      note: 'لاختبار الاتصال الفعلي، استخدم: node scripts/test-xampp-connection.js'
    });

  } else if (path === '/api/auth/login' && method === 'POST') {
    parseBody(req, (error, body) => {
      if (error) {
        sendJSON(res, 400, {
          success: false,
          message: 'خطأ في تحليل البيانات',
          errors: ['INVALID_JSON']
        });
        return;
      }

      const { email, password } = body;

      if (email === '<EMAIL>' && password === 'admin123') {
        sendJSON(res, 200, {
          success: true,
          message: 'تم تسجيل الدخول بنجاح! 🎉',
          data: {
            user: {
              id: 'demo-user-1',
              tenantId: 'demo-tenant-1',
              email: '<EMAIL>',
              firstName: 'أحمد',
              lastName: 'المدير',
              role: 'SUPER_ADMIN',
              tenant: {
                id: 'demo-tenant-1',
                name: 'شركة التقنية المتقدمة',
                domain: 'advanced-tech.local'
              }
            },
            token: 'mock-jwt-token-for-testing-' + Date.now(),
            refreshToken: 'mock-refresh-token-' + Date.now(),
            expiresIn: '7d'
          }
        });
      } else {
        sendJSON(res, 401, {
          success: false,
          message: 'بيانات تسجيل الدخول غير صحيحة ❌',
          errors: ['INVALID_CREDENTIALS'],
          hint: 'استخدم: <EMAIL> / admin123'
        });
      }
    });

  } else if (path === '/api/clients' && method === 'GET') {
    sendJSON(res, 200, {
      success: true,
      message: 'تم جلب العملاء بنجاح',
      data: [
        {
          id: 'demo-client-1',
          tenantId: 'demo-tenant-1',
          name: 'شركة البناء الحديث',
          email: '<EMAIL>',
          phone: '+966112345678',
          address: 'شارع الملك فهد، حي العليا',
          city: 'الرياض',
          country: 'المملكة العربية السعودية',
          taxNumber: '1234567890',
          isActive: true,
          createdAt: '2024-01-10T09:00:00.000Z'
        },
        {
          id: 'demo-client-2',
          tenantId: 'demo-tenant-1',
          name: 'مؤسسة التجارة الإلكترونية',
          email: '<EMAIL>',
          phone: '+966113456789',
          address: 'طريق الأمير محمد بن عبدالعزيز',
          city: 'الرياض',
          country: 'المملكة العربية السعودية',
          taxNumber: '0987654321',
          isActive: true,
          createdAt: '2024-01-12T10:30:00.000Z'
        },
        {
          id: 'demo-client-3',
          tenantId: 'demo-tenant-2',
          name: 'شركة الخدمات المالية',
          email: '<EMAIL>',
          phone: '+966114567890',
          address: 'شارع التحلية',
          city: 'جدة',
          country: 'المملكة العربية السعودية',
          taxNumber: '1122334455',
          isActive: true,
          createdAt: '2024-01-15T14:00:00.000Z'
        }
      ],
      pagination: {
        page: 1,
        limit: 10,
        total: 3,
        totalPages: 1
      }
    });

  } else if (path === '/api/invoices' && method === 'GET') {
    sendJSON(res, 200, {
      success: true,
      message: 'تم جلب الفواتير بنجاح',
      data: [
        {
          id: 'demo-invoice-1',
          tenantId: 'demo-tenant-1',
          invoiceNumber: 'INV-2024-001',
          status: 'SENT',
          issueDate: '2024-01-15T10:00:00.000Z',
          dueDate: '2024-02-14T23:59:59.000Z',
          subtotal: 10000.00,
          taxRate: 0.15,
          taxAmount: 1500.00,
          totalAmount: 11500.00,
          currency: 'SAR',
          client: {
            id: 'demo-client-1',
            name: 'شركة البناء الحديث',
            email: '<EMAIL>'
          },
          user: {
            id: 'demo-user-1',
            firstName: 'أحمد',
            lastName: 'المدير'
          },
          items: [
            {
              id: 'demo-item-1',
              description: 'تصميم وتطوير الموقع الإلكتروني',
              quantity: 1,
              unitPrice: 6000.00,
              totalPrice: 6000.00
            },
            {
              id: 'demo-item-2',
              description: 'برمجة نظام إدارة المحتوى',
              quantity: 1,
              unitPrice: 3000.00,
              totalPrice: 3000.00
            },
            {
              id: 'demo-item-3',
              description: 'اختبار وضمان الجودة',
              quantity: 10,
              unitPrice: 100.00,
              totalPrice: 1000.00
            }
          ]
        },
        {
          id: 'demo-invoice-2',
          tenantId: 'demo-tenant-1',
          invoiceNumber: 'INV-2024-002',
          status: 'PAID',
          issueDate: '2024-01-20T14:30:00.000Z',
          dueDate: '2024-02-19T23:59:59.000Z',
          subtotal: 7500.00,
          taxRate: 0.15,
          taxAmount: 1125.00,
          discountAmount: 375.00,
          totalAmount: 8250.00,
          currency: 'SAR',
          paidAt: '2024-02-10T11:30:00.000Z',
          client: {
            id: 'demo-client-2',
            name: 'مؤسسة التجارة الإلكترونية',
            email: '<EMAIL>'
          },
          user: {
            id: 'demo-user-2',
            firstName: 'فاطمة',
            lastName: 'المحاسبة'
          },
          items: [
            {
              id: 'demo-item-4',
              description: 'استشارات تقنية متخصصة',
              quantity: 15,
              unitPrice: 300.00,
              totalPrice: 4500.00
            },
            {
              id: 'demo-item-5',
              description: 'تحليل وتقييم الأنظمة',
              quantity: 1,
              unitPrice: 2000.00,
              totalPrice: 2000.00
            },
            {
              id: 'demo-item-6',
              description: 'إعداد التقارير الفنية',
              quantity: 5,
              unitPrice: 200.00,
              totalPrice: 1000.00
            }
          ]
        }
      ],
      pagination: {
        page: 1,
        limit: 10,
        total: 2,
        totalPages: 1
      }
    });

  } else if (path === '/api/dashboard/stats' && method === 'GET') {
    sendJSON(res, 200, {
      success: true,
      message: 'تم جلب إحصائيات لوحة التحكم بنجاح',
      data: {
        totalClients: 3,
        totalInvoices: 3,
        paidInvoices: 1,
        overdueInvoices: 1,
        totalRevenue: 8250.00,
        pendingAmount: 11500.00,
        thisMonthRevenue: 8250.00,
        lastMonthRevenue: 0,
        recentInvoices: [
          {
            id: 'demo-invoice-1',
            invoiceNumber: 'INV-2024-001',
            status: 'SENT',
            totalAmount: 11500.00,
            currency: 'SAR',
            client: { name: 'شركة البناء الحديث' },
            daysOverdue: 18
          },
          {
            id: 'demo-invoice-2',
            invoiceNumber: 'INV-2024-002',
            status: 'PAID',
            totalAmount: 8250.00,
            currency: 'SAR',
            client: { name: 'مؤسسة التجارة الإلكترونية' }
          }
        ],
        topClients: [
          {
            id: 'demo-client-1',
            name: 'شركة البناء الحديث',
            totalInvoices: 1,
            totalAmount: 11500.00,
            paidAmount: 0,
            pendingAmount: 11500.00
          },
          {
            id: 'demo-client-2',
            name: 'مؤسسة التجارة الإلكترونية',
            totalInvoices: 1,
            totalAmount: 8250.00,
            paidAmount: 8250.00,
            pendingAmount: 0
          }
        ]
      }
    });

  } else {
    // 404 Not Found
    sendJSON(res, 404, {
      success: false,
      message: 'المسار غير موجود ❌',
      error: 'NOT_FOUND',
      requestedPath: path,
      method: method,
      availableEndpoints: [
        'GET /health - فحص صحة الخادم',
        'GET /api - معلومات API',
        'GET /api/test-db - اختبار قاعدة البيانات',
        'POST /api/auth/login - تسجيل الدخول',
        'GET /api/clients - قائمة العملاء',
        'GET /api/invoices - قائمة الفواتير',
        'GET /api/dashboard/stats - إحصائيات لوحة التحكم'
      ],
      hint: 'جرب: GET /api للحصول على معلومات شاملة'
    });
  }
});

// Start server
server.listen(PORT, () => {
  console.log('========================================');
  console.log('🚀 نظام إدارة الفواتير - Web Invoice SaaS');
  console.log('========================================');
  console.log(`📡 الخادم يعمل على المنفذ: ${PORT}`);
  console.log(`🌐 الرابط الأساسي: http://localhost:${PORT}`);
  console.log('');
  console.log('🔗 الروابط المتاحة:');
  console.log(`   🏥 فحص الصحة: http://localhost:${PORT}/health`);
  console.log(`   📋 معلومات API: http://localhost:${PORT}/api`);
  console.log(`   🧪 اختبار قاعدة البيانات: http://localhost:${PORT}/api/test-db`);
  console.log(`   🔐 تسجيل الدخول: POST http://localhost:${PORT}/api/auth/login`);
  console.log(`   👥 العملاء: http://localhost:${PORT}/api/clients`);
  console.log(`   📄 الفواتير: http://localhost:${PORT}/api/invoices`);
  console.log(`   📊 لوحة التحكم: http://localhost:${PORT}/api/dashboard/stats`);
  console.log('');
  console.log('👤 الحساب التجريبي:');
  console.log('   📧 البريد الإلكتروني: <EMAIL>');
  console.log('   🔑 كلمة المرور: admin123');
  console.log('   👑 الدور: مدير عام (SUPER_ADMIN)');
  console.log('');
  console.log('✨ النظام جاهز للاستخدام!');
  console.log('========================================');
});

// Handle server errors
server.on('error', (error) => {
  console.error('❌ خطأ في الخادم:', error.message);
  if (error.code === 'EADDRINUSE') {
    console.log(`⚠️  المنفذ ${PORT} مستخدم بالفعل`);
    console.log('💡 جرب منفذ آخر: PORT=5001 node server-basic.js');
  }
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف الخادم...');
  server.close(() => {
    console.log('✅ تم إيقاف الخادم بنجاح');
    process.exit(0);
  });
});

module.exports = server;
