import { Request, Response } from 'express';
import { DashboardService } from '@/services/dashboardService';

export class DashboardController {
  // الحصول على إحصائيات لوحة التحكم
  static async getDashboardStats(req: Request, res: Response) {
    try {
      if (!req.user || !req.tenant) {
        return res.status(401).json({
          success: false,
          message: 'المصادقة مطلوبة',
          errors: ['AUTHENTICATION_REQUIRED'],
        });
      }

      const result = await DashboardService.getDashboardStats(req.user.tenantId);

      if (!result.success) {
        return res.status(400).json(result);
      }

      return res.status(200).json(result);
    } catch (error) {
      console.error('Get dashboard stats controller error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ داخلي في الخادم',
        errors: ['INTERNAL_ERROR'],
      });
    }
  }

  // الحصول على إحصائيات المبيعات الشهرية
  static async getMonthlySalesStats(req: Request, res: Response) {
    try {
      if (!req.user || !req.tenant) {
        return res.status(401).json({
          success: false,
          message: 'المصادقة مطلوبة',
          errors: ['AUTHENTICATION_REQUIRED'],
        });
      }

      const year = req.query.year ? parseInt(req.query.year as string) : undefined;

      const result = await DashboardService.getMonthlySalesStats(req.user.tenantId, year);

      if (!result.success) {
        return res.status(400).json(result);
      }

      return res.status(200).json(result);
    } catch (error) {
      console.error('Get monthly sales stats controller error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ داخلي في الخادم',
        errors: ['INTERNAL_ERROR'],
      });
    }
  }

  // الحصول على إحصائيات حالة الفواتير
  static async getInvoiceStatusStats(req: Request, res: Response) {
    try {
      if (!req.user || !req.tenant) {
        return res.status(401).json({
          success: false,
          message: 'المصادقة مطلوبة',
          errors: ['AUTHENTICATION_REQUIRED'],
        });
      }

      const result = await DashboardService.getInvoiceStatusStats(req.user.tenantId);

      if (!result.success) {
        return res.status(400).json(result);
      }

      return res.status(200).json(result);
    } catch (error) {
      console.error('Get invoice status stats controller error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ داخلي في الخادم',
        errors: ['INTERNAL_ERROR'],
      });
    }
  }

  // الحصول على الفواتير المتأخرة
  static async getOverdueInvoices(req: Request, res: Response) {
    try {
      if (!req.user || !req.tenant) {
        return res.status(401).json({
          success: false,
          message: 'المصادقة مطلوبة',
          errors: ['AUTHENTICATION_REQUIRED'],
        });
      }

      const result = await DashboardService.getOverdueInvoices(req.user.tenantId);

      if (!result.success) {
        return res.status(400).json(result);
      }

      return res.status(200).json(result);
    } catch (error) {
      console.error('Get overdue invoices controller error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ داخلي في الخادم',
        errors: ['INTERNAL_ERROR'],
      });
    }
  }
}
