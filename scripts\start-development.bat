@echo off
chcp 65001 >nul
title تشغيل نظام إدارة الفواتير - وضع التطوير

echo ========================================
echo      تشغيل نظام إدارة الفواتير
echo           وضع التطوير
echo ========================================
echo.

REM الحصول على مسار المشروع
set PROJECT_ROOT=%~dp0..
cd /d "%PROJECT_ROOT%"

echo 📁 مسار المشروع: %CD%
echo.

REM التحقق من وجود ملف .env
if not exist "backend\.env" (
    echo ❌ ملف .env غير موجود في مجلد backend
    echo يرجى نسخ .env.example إلى .env وتحديث الإعدادات
    echo.
    echo تشغيل الأمر التالي:
    echo copy backend\.env.example backend\.env
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على ملف .env
echo.

REM التحقق من تثبيت التبعيات
if not exist "backend\node_modules" (
    echo ⚠️  التبعيات غير مثبتة، جاري التثبيت...
    cd backend
    call npm install
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
    cd ..
    echo ✅ تم تثبيت التبعيات بنجاح
    echo.
)

REM اختبار الاتصال بقاعدة البيانات
echo 🧪 اختبار الاتصال بقاعدة البيانات...
if exist "scripts\test-xampp-connection.js" (
    node scripts\test-xampp-connection.js
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل اختبار الاتصال بقاعدة البيانات
        echo يرجى التأكد من:
        echo 1. تشغيل XAMPP (Apache + MySQL)
        echo 2. استيراد ملف قاعدة البيانات
        echo 3. صحة إعدادات DATABASE_URL في .env
        echo.
        pause
        exit /b 1
    )
    echo ✅ اختبار قاعدة البيانات نجح
    echo.
)

echo ========================================
echo           تشغيل الخوادم
echo ========================================
echo.

REM تشغيل Backend
echo 🚀 تشغيل Backend API Server...
cd backend
start cmd /k "title Backend API Server - http://localhost:5000 && echo 🚀 Backend API Server && echo المنفذ: 5000 && echo الحالة: يعمل && echo. && echo للإيقاف: اضغط Ctrl+C && echo. && npm run dev"
cd ..

REM انتظار قليل لبدء Backend
timeout /t 3 >nul

REM تشغيل Frontend إذا كان موجوداً
if exist "frontend" (
    echo 🌐 تشغيل Frontend Web Application...
    cd frontend
    
    REM التحقق من تثبيت التبعيات
    if not exist "node_modules" (
        echo ⚠️  تثبيت تبعيات Frontend...
        call npm install
        if %ERRORLEVEL% NEQ 0 (
            echo ❌ فشل في تثبيت تبعيات Frontend
            echo سيتم تشغيل Backend فقط
            cd ..
            goto :skip_frontend
        )
    )
    
    start cmd /k "title Frontend Web App - http://localhost:3000 && echo 🌐 Frontend Web Application && echo المنفذ: 3000 && echo الحالة: يعمل && echo. && echo للإيقاف: اضغط Ctrl+C && echo. && npm run dev"
    cd ..
    
    :skip_frontend
) else (
    echo ⚠️  مجلد Frontend غير موجود
    echo سيتم تشغيل Backend API فقط
    echo.
    echo لإنشاء Frontend:
    echo npx create-next-app@latest frontend --typescript --tailwind --eslint
)

echo.
echo ========================================
echo            النظام يعمل الآن!
echo ========================================
echo.

echo 🌐 الروابط المتاحة:
echo.
echo    📡 Backend API:
echo       الصحة: http://localhost:5000/health
echo       المعلومات: http://localhost:5000/api
echo       المصادقة: http://localhost:5000/api/auth
echo       العملاء: http://localhost:5000/api/clients
echo       الفواتير: http://localhost:5000/api/invoices
echo       لوحة التحكم: http://localhost:5000/api/dashboard
echo.

if exist "frontend" (
    echo    🌐 Frontend Web App:
    echo       الرئيسية: http://localhost:3000
    echo.
)

echo    🗄️  إدارة قاعدة البيانات:
echo       phpMyAdmin: http://localhost/phpmyadmin
echo       Prisma Studio: npx prisma studio (في مجلد backend)
echo.

echo 👤 حساب المدير التجريبي:
echo    البريد الإلكتروني: <EMAIL>
echo    كلمة المرور: admin123
echo    الدور: مدير عام (SUPER_ADMIN)
echo.

echo 📋 أوامر مفيدة:
echo    إيقاف الخوادم: أغلق نوافذ الخوادم أو اضغط Ctrl+C
echo    إعادة تشغيل: شغل هذا السكريبت مرة أخرى
echo    عرض السجلات: تحقق من نوافذ الخوادم
echo    اختبار API: curl http://localhost:5000/health
echo.

echo 🔧 للتطوير:
echo    Backend: cd backend && npm run dev
echo    Frontend: cd frontend && npm run dev
echo    قاعدة البيانات: cd backend && npx prisma studio
echo    الاختبارات: cd backend && npm test
echo.

REM فتح المتصفح (اختياري)
set /p OPEN_BROWSER="هل تريد فتح النظام في المتصفح؟ (y/n): "
if /i "%OPEN_BROWSER%"=="y" (
    echo 🌐 فتح المتصفح...
    
    REM فتح Backend API info
    start http://localhost:5000/api
    
    REM فتح Frontend إذا كان موجوداً
    if exist "frontend" (
        timeout /t 2 >nul
        start http://localhost:3000
    )
    
    REM فتح phpMyAdmin
    timeout /t 1 >nul
    start http://localhost/phpmyadmin
)

echo.
echo ✨ النظام جاهز للاستخدام!
echo 💡 لإيقاف النظام، أغلق نوافذ الخوادم أو اضغط Ctrl+C في كل نافذة
echo.

REM إبقاء النافذة مفتوحة
echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
