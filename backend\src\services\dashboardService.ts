import prisma from '@/config/database';
import { DashboardStats, ApiResponse, InvoiceWithDetails, ClientWithStats } from '@/types/models';

export class DashboardService {
  // الحصول على إحصائيات لوحة التحكم
  static async getDashboardStats(tenantId: string): Promise<ApiResponse<DashboardStats>> {
    try {
      // تواريخ للمقارنة
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

      // الحصول على الإحصائيات الأساسية
      const [
        totalClients,
        totalInvoices,
        paidInvoices,
        overdueInvoices,
        totalRevenue,
        pendingAmount,
        thisMonthRevenue,
        lastMonthRevenue,
      ] = await Promise.all([
        // إجمالي العملاء النشطين
        prisma.client.count({
          where: { tenantId, isActive: true },
        }),

        // إجمالي الفواتير
        prisma.invoice.count({
          where: { tenantId },
        }),

        // الفواتير المدفوعة
        prisma.invoice.count({
          where: { tenantId, status: 'PAID' },
        }),

        // الفواتير المتأخرة
        prisma.invoice.count({
          where: { 
            tenantId, 
            status: 'SENT',
            dueDate: { lt: now },
          },
        }),

        // إجمالي الإيرادات (الفواتير المدفوعة)
        prisma.invoice.aggregate({
          where: { tenantId, status: 'PAID' },
          _sum: { totalAmount: true },
        }),

        // المبلغ المعلق (الفواتير المرسلة)
        prisma.invoice.aggregate({
          where: { tenantId, status: 'SENT' },
          _sum: { totalAmount: true },
        }),

        // إيرادات هذا الشهر
        prisma.invoice.aggregate({
          where: { 
            tenantId, 
            status: 'PAID',
            paidAt: { gte: startOfMonth },
          },
          _sum: { totalAmount: true },
        }),

        // إيرادات الشهر الماضي
        prisma.invoice.aggregate({
          where: { 
            tenantId, 
            status: 'PAID',
            paidAt: { 
              gte: startOfLastMonth,
              lte: endOfLastMonth,
            },
          },
          _sum: { totalAmount: true },
        }),
      ]);

      // الحصول على أحدث الفواتير
      const recentInvoices = await prisma.invoice.findMany({
        where: { tenantId },
        include: {
          client: true,
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          items: {
            orderBy: { sortOrder: 'asc' },
          },
          payments: {
            orderBy: { paymentDate: 'desc' },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: 5,
      });

      // الحصول على أفضل العملاء
      const topClientsData = await prisma.client.findMany({
        where: { tenantId, isActive: true },
        include: {
          _count: {
            select: { invoices: true },
          },
        },
        take: 5,
      });

      // حساب إحصائيات العملاء
      const topClients: ClientWithStats[] = await Promise.all(
        topClientsData.map(async (client) => {
          const [totalAmount, paidAmount] = await Promise.all([
            prisma.invoice.aggregate({
              where: { clientId: client.id, tenantId },
              _sum: { totalAmount: true },
            }),
            prisma.invoice.aggregate({
              where: { clientId: client.id, tenantId, status: 'PAID' },
              _sum: { totalAmount: true },
            }),
          ]);

          return {
            ...client,
            totalInvoices: client._count.invoices,
            totalAmount: Number(totalAmount._sum.totalAmount) || 0,
            paidAmount: Number(paidAmount._sum.totalAmount) || 0,
            pendingAmount: (Number(totalAmount._sum.totalAmount) || 0) - (Number(paidAmount._sum.totalAmount) || 0),
          };
        })
      );

      // ترتيب العملاء حسب إجمالي المبلغ
      topClients.sort((a, b) => b.totalAmount - a.totalAmount);

      const stats: DashboardStats = {
        totalClients,
        totalInvoices,
        paidInvoices,
        overdueInvoices,
        totalRevenue: Number(totalRevenue._sum.totalAmount) || 0,
        pendingAmount: Number(pendingAmount._sum.totalAmount) || 0,
        thisMonthRevenue: Number(thisMonthRevenue._sum.totalAmount) || 0,
        lastMonthRevenue: Number(lastMonthRevenue._sum.totalAmount) || 0,
        recentInvoices: recentInvoices as InvoiceWithDetails[],
        topClients: topClients.slice(0, 5),
      };

      return {
        success: true,
        message: 'تم جلب إحصائيات لوحة التحكم بنجاح',
        data: stats,
      };
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      return {
        success: false,
        message: 'فشل في جلب إحصائيات لوحة التحكم',
        errors: ['FETCH_DASHBOARD_STATS_FAILED'],
      };
    }
  }

  // الحصول على إحصائيات المبيعات الشهرية
  static async getMonthlySalesStats(tenantId: string, year?: number): Promise<ApiResponse<any[]>> {
    try {
      const targetYear = year || new Date().getFullYear();
      
      const monthlySales = await prisma.$queryRaw`
        SELECT 
          MONTH(paidAt) as month,
          COUNT(*) as invoice_count,
          SUM(totalAmount) as total_amount
        FROM invoices 
        WHERE tenantId = ${tenantId}
          AND status = 'PAID'
          AND YEAR(paidAt) = ${targetYear}
        GROUP BY MONTH(paidAt)
        ORDER BY MONTH(paidAt)
      `;

      // تحويل النتائج إلى تنسيق مناسب
      const monthlyData = Array.from({ length: 12 }, (_, index) => {
        const month = index + 1;
        const data = (monthlySales as any[]).find(item => item.month === month);
        return {
          month,
          monthName: getMonthName(month),
          invoiceCount: data ? Number(data.invoice_count) : 0,
          totalAmount: data ? Number(data.total_amount) : 0,
        };
      });

      return {
        success: true,
        message: 'تم جلب إحصائيات المبيعات الشهرية بنجاح',
        data: monthlyData,
      };
    } catch (error) {
      console.error('Error fetching monthly sales stats:', error);
      return {
        success: false,
        message: 'فشل في جلب إحصائيات المبيعات الشهرية',
        errors: ['FETCH_MONTHLY_SALES_FAILED'],
      };
    }
  }

  // الحصول على إحصائيات حالة الفواتير
  static async getInvoiceStatusStats(tenantId: string): Promise<ApiResponse<any[]>> {
    try {
      const statusStats = await prisma.invoice.groupBy({
        by: ['status'],
        where: { tenantId },
        _count: { status: true },
        _sum: { totalAmount: true },
      });

      const formattedStats = statusStats.map(stat => ({
        status: stat.status,
        statusArabic: getStatusArabic(stat.status),
        count: stat._count.status,
        totalAmount: Number(stat._sum.totalAmount) || 0,
      }));

      return {
        success: true,
        message: 'تم جلب إحصائيات حالة الفواتير بنجاح',
        data: formattedStats,
      };
    } catch (error) {
      console.error('Error fetching invoice status stats:', error);
      return {
        success: false,
        message: 'فشل في جلب إحصائيات حالة الفواتير',
        errors: ['FETCH_STATUS_STATS_FAILED'],
      };
    }
  }

  // الحصول على الفواتير المتأخرة
  static async getOverdueInvoices(tenantId: string): Promise<ApiResponse<InvoiceWithDetails[]>> {
    try {
      const overdueInvoices = await prisma.invoice.findMany({
        where: {
          tenantId,
          status: 'SENT',
          dueDate: { lt: new Date() },
        },
        include: {
          client: true,
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          items: {
            orderBy: { sortOrder: 'asc' },
          },
          payments: {
            orderBy: { paymentDate: 'desc' },
          },
        },
        orderBy: { dueDate: 'asc' },
      });

      return {
        success: true,
        message: 'تم جلب الفواتير المتأخرة بنجاح',
        data: overdueInvoices as InvoiceWithDetails[],
      };
    } catch (error) {
      console.error('Error fetching overdue invoices:', error);
      return {
        success: false,
        message: 'فشل في جلب الفواتير المتأخرة',
        errors: ['FETCH_OVERDUE_INVOICES_FAILED'],
      };
    }
  }
}

// دوال مساعدة
function getMonthName(month: number): string {
  const months = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];
  return months[month - 1];
}

function getStatusArabic(status: string): string {
  const statusMap: { [key: string]: string } = {
    'DRAFT': 'مسودة',
    'SENT': 'مرسلة',
    'PAID': 'مدفوعة',
    'OVERDUE': 'متأخرة',
    'CANCELLED': 'ملغية',
  };
  return statusMap[status] || status;
}
