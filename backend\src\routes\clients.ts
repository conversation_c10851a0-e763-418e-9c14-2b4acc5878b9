import { Router } from 'express';
import { ClientController } from '@/controllers/clientController';
import { authenticate, authorize } from '@/middleware/auth';

const router = Router();

// جميع المسارات تتطلب مصادقة
router.use(authenticate);

// الحصول على جميع العملاء
router.get('/', 
  authorize(['clients:read']),
  ClientController.getClients
);

// البحث في العملاء
router.get('/search', 
  authorize(['clients:read']),
  ClientController.searchClients
);

// الحصول على عميل واحد
router.get('/:clientId', 
  authorize(['clients:read']),
  ClientController.getClientById
);

// الحصول على إحصائيات العميل
router.get('/:clientId/stats', 
  authorize(['clients:read']),
  ClientController.getClientStats
);

// إنشاء عميل جديد
router.post('/', 
  authorize(['clients:write']),
  ClientController.createClient
);

// تحديث عميل
router.put('/:clientId', 
  authorize(['clients:write']),
  ClientController.updateClient
);

// حذف عميل
router.delete('/:clientId', 
  authorize(['clients:delete']),
  ClientController.deleteClient
);

export default router;
