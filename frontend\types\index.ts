// User and Authentication Types
export interface User {
  id: string;
  tenantId: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: 'USER' | 'ADMIN' | 'SUPER_ADMIN';
  isActive: boolean;
  lastLoginAt?: string;
  emailVerifiedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Tenant {
  id: string;
  name: string;
  domain?: string;
  settings?: any;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  data?: {
    user: User;
    tenant: Tenant;
    token: string;
    refreshToken: string;
    expiresIn: string;
  };
  errors?: string[];
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  companyName: string;
  phone?: string;
}

// Client Types
export interface Client {
  id: string;
  tenantId: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  taxNumber?: string;
  notes?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ClientWithStats extends Client {
  totalInvoices: number;
  totalAmount: number;
  paidAmount: number;
  pendingAmount: number;
}

export interface CreateClientRequest {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  taxNumber?: string;
  notes?: string;
}

export interface UpdateClientRequest extends Partial<CreateClientRequest> {
  isActive?: boolean;
}

// Invoice Types
export interface Invoice {
  id: string;
  tenantId: string;
  clientId: string;
  userId: string;
  invoiceNumber: string;
  status: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED';
  issueDate: string;
  dueDate: string;
  subtotal: number;
  taxRate: number;
  taxAmount: number;
  discountRate: number;
  discountAmount: number;
  totalAmount: number;
  currency: string;
  notes?: string;
  terms?: string;
  paidAt?: string;
  sentAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface InvoiceItem {
  id: string;
  invoiceId: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export interface Payment {
  id: string;
  tenantId: string;
  invoiceId: string;
  amount: number;
  paymentMethod: 'CASH' | 'BANK_TRANSFER' | 'CREDIT_CARD' | 'PAYPAL' | 'STRIPE' | 'OTHER';
  paymentDate: string;
  reference?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface InvoiceWithDetails extends Invoice {
  client: Client;
  user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
  items: InvoiceItem[];
  payments: Payment[];
}

export interface CreateInvoiceRequest {
  clientId: string;
  title?: string;
  description?: string;
  dueDate: string;
  currency?: string;
  taxRate?: number;
  discountRate?: number;
  notes?: string;
  terms?: string;
  items: CreateInvoiceItemRequest[];
}

export interface CreateInvoiceItemRequest {
  description: string;
  quantity: number;
  unitPrice: number;
}

export interface UpdateInvoiceRequest extends Partial<CreateInvoiceRequest> {
  status?: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED';
}

// Dashboard Types
export interface DashboardStats {
  totalClients: number;
  totalInvoices: number;
  paidInvoices: number;
  overdueInvoices: number;
  totalRevenue: number;
  pendingAmount: number;
  thisMonthRevenue: number;
  lastMonthRevenue: number;
  recentInvoices: InvoiceWithDetails[];
  topClients: ClientWithStats[];
}

export interface MonthlySalesData {
  month: number;
  monthName: string;
  invoiceCount: number;
  totalAmount: number;
}

export interface InvoiceStatusStats {
  status: string;
  statusArabic: string;
  count: number;
  totalAmount: number;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: string[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface InvoiceFilters extends PaginationParams {
  status?: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED';
  clientId?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
}

export interface ClientFilters extends PaginationParams {
  search?: string;
  isActive?: boolean;
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'date';
  placeholder?: string;
  required?: boolean;
  options?: { value: string; label: string }[];
  validation?: any;
}

// UI Types
export interface MenuItem {
  id: string;
  label: string;
  icon?: React.ComponentType<any>;
  href?: string;
  onClick?: () => void;
  children?: MenuItem[];
  badge?: string | number;
  disabled?: boolean;
}

export interface TableColumn<T = any> {
  key: string;
  label: string;
  sortable?: boolean;
  render?: (value: any, row: T) => React.ReactNode;
  className?: string;
}

export interface TableProps<T = any> {
  data: T[];
  columns: TableColumn<T>[];
  loading?: boolean;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    onPageChange: (page: number) => void;
  };
  onSort?: (key: string, direction: 'asc' | 'desc') => void;
  className?: string;
}

// Notification Types
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// Settings Types
export interface TenantSettings {
  id: string;
  tenantId: string;
  invoicePrefix: string;
  invoiceNumbering: 'SEQUENTIAL' | 'RANDOM' | 'CUSTOM';
  nextInvoiceNumber: number;
  defaultDueDays: number;
  defaultCurrency: string;
  defaultTaxRate: number;
  companyLogo?: string;
  companyAddress?: string;
  companyPhone?: string;
  companyEmail?: string;
  companyWebsite?: string;
  emailSignature?: string;
  invoiceFooter?: string;
  invoiceTerms?: string;
  allowOnlinePayment: boolean;
  createdAt: string;
  updatedAt: string;
}

// Error Types
export interface ApiError {
  message: string;
  code?: string;
  field?: string;
  details?: any;
}

// Chart Types
export interface ChartData {
  name: string;
  value: number;
  color?: string;
}

export interface LineChartData {
  name: string;
  [key: string]: string | number;
}

// Export all types
export type {
  User,
  Tenant,
  AuthResponse,
  LoginRequest,
  RegisterRequest,
  Client,
  ClientWithStats,
  CreateClientRequest,
  UpdateClientRequest,
  Invoice,
  InvoiceItem,
  Payment,
  InvoiceWithDetails,
  CreateInvoiceRequest,
  CreateInvoiceItemRequest,
  UpdateInvoiceRequest,
  DashboardStats,
  MonthlySalesData,
  InvoiceStatusStats,
  ApiResponse,
  PaginationParams,
  InvoiceFilters,
  ClientFilters,
  FormField,
  MenuItem,
  TableColumn,
  TableProps,
  Notification,
  TenantSettings,
  ApiError,
  ChartData,
  LineChartData,
};
