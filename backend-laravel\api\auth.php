<?php
require_once '../config/database.php';

setCorsHeaders();

class AuthAPI {
    private $db;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }

    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $path_parts = explode('/', trim($path, '/'));
        
        if (!isset($path_parts[2])) {
            errorResponse('نقطة نهاية غير صحيحة', 400);
        }
        
        $endpoint = $path_parts[2];
        
        switch ($endpoint) {
            case 'login':
                if ($method === 'POST') {
                    $this->login();
                } else {
                    errorResponse('طريقة غير مدعومة', 405);
                }
                break;
            case 'register':
                if ($method === 'POST') {
                    $this->register();
                } else {
                    errorResponse('طريقة غير مدعومة', 405);
                }
                break;
            case 'profile':
                if ($method === 'GET') {
                    $this->getProfile();
                } else {
                    errorResponse('طريقة غير مدعومة', 405);
                }
                break;
            case 'logout':
                if ($method === 'POST') {
                    $this->logout();
                } else {
                    errorResponse('طريقة غير مدعومة', 405);
                }
                break;
            default:
                errorResponse('نقطة نهاية غير موجودة', 404);
        }
    }

    public function login() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (empty($input['email']) || empty($input['password'])) {
                errorResponse('البريد الإلكتروني وكلمة المرور مطلوبان', 400);
            }
            
            // Get user with tenant information
            $sql = "SELECT u.*, t.name as tenant_name, t.domain as tenant_domain 
                    FROM users u 
                    JOIN tenants t ON u.tenant_id = t.id 
                    WHERE u.email = :email AND u.is_active = 1 AND t.is_active = 1";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':email', $input['email']);
            $stmt->execute();
            
            $user = $stmt->fetch();
            
            if (!$user || !password_verify($input['password'], $user['password_hash'])) {
                errorResponse('البريد الإلكتروني أو كلمة المرور غير صحيحة', 401);
            }
            
            // Generate JWT token (simplified version)
            $token = $this->generateToken($user['id'], $user['tenant_id']);
            
            $response_data = [
                'user' => [
                    'id' => (string)$user['id'],
                    'firstName' => $user['first_name'],
                    'lastName' => $user['last_name'],
                    'email' => $user['email'],
                    'phone' => $user['phone'],
                    'role' => $user['role'],
                    'createdAt' => $user['created_at'],
                    'updatedAt' => $user['updated_at']
                ],
                'tenant' => [
                    'id' => (string)$user['tenant_id'],
                    'name' => $user['tenant_name'],
                    'domain' => $user['tenant_domain']
                ],
                'token' => $token
            ];
            
            successResponse($response_data, 'تم تسجيل الدخول بنجاح');
            
        } catch (Exception $e) {
            errorResponse('خطأ في تسجيل الدخول: ' . $e->getMessage(), 500);
        }
    }

    public function register() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            // Validate required fields
            $required_fields = ['firstName', 'lastName', 'email', 'password', 'companyName'];
            foreach ($required_fields as $field) {
                if (empty($input[$field])) {
                    errorResponse("الحقل $field مطلوب", 400);
                }
            }
            
            // Validate email format
            if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
                errorResponse('البريد الإلكتروني غير صحيح', 400);
            }
            
            // Check if email already exists
            $check_sql = "SELECT id FROM users WHERE email = :email";
            $check_stmt = $this->db->prepare($check_sql);
            $check_stmt->bindParam(':email', $input['email']);
            $check_stmt->execute();
            
            if ($check_stmt->fetch()) {
                errorResponse('البريد الإلكتروني مستخدم بالفعل', 400);
            }
            
            // Start transaction
            $this->db->beginTransaction();
            
            try {
                // Create tenant
                $tenant_domain = strtolower(preg_replace('/[^a-zA-Z0-9]/', '', $input['companyName'])) . '_' . time();
                
                $tenant_sql = "INSERT INTO tenants (name, domain, is_active) VALUES (:name, :domain, 1)";
                $tenant_stmt = $this->db->prepare($tenant_sql);
                $tenant_stmt->bindParam(':name', $input['companyName']);
                $tenant_stmt->bindParam(':domain', $tenant_domain);
                $tenant_stmt->execute();
                
                $tenant_id = $this->db->lastInsertId();
                
                // Create user
                $password_hash = password_hash($input['password'], PASSWORD_DEFAULT);
                
                $user_sql = "INSERT INTO users (tenant_id, first_name, last_name, email, phone, password_hash, role, is_active) 
                            VALUES (:tenant_id, :first_name, :last_name, :email, :phone, :password_hash, 'admin', 1)";
                
                $user_stmt = $this->db->prepare($user_sql);
                $user_stmt->bindParam(':tenant_id', $tenant_id);
                $user_stmt->bindParam(':first_name', $input['firstName']);
                $user_stmt->bindParam(':last_name', $input['lastName']);
                $user_stmt->bindParam(':email', $input['email']);
                $user_stmt->bindParam(':phone', $input['phone']);
                $user_stmt->bindParam(':password_hash', $password_hash);
                $user_stmt->execute();
                
                $user_id = $this->db->lastInsertId();
                
                // Commit transaction
                $this->db->commit();
                
                // Generate JWT token
                $token = $this->generateToken($user_id, $tenant_id);
                
                $response_data = [
                    'user' => [
                        'id' => (string)$user_id,
                        'firstName' => $input['firstName'],
                        'lastName' => $input['lastName'],
                        'email' => $input['email'],
                        'phone' => $input['phone'],
                        'role' => 'admin',
                        'createdAt' => date('Y-m-d H:i:s'),
                        'updatedAt' => date('Y-m-d H:i:s')
                    ],
                    'tenant' => [
                        'id' => (string)$tenant_id,
                        'name' => $input['companyName'],
                        'domain' => $tenant_domain
                    ],
                    'token' => $token
                ];
                
                successResponse($response_data, 'تم إنشاء الحساب بنجاح');
                
            } catch (Exception $e) {
                $this->db->rollback();
                throw $e;
            }
            
        } catch (Exception $e) {
            errorResponse('خطأ في إنشاء الحساب: ' . $e->getMessage(), 500);
        }
    }

    public function getProfile() {
        try {
            $token = $this->getTokenFromHeader();
            if (!$token) {
                errorResponse('رمز المصادقة مطلوب', 401);
            }
            
            $user_data = $this->validateToken($token);
            if (!$user_data) {
                errorResponse('رمز المصادقة غير صحيح', 401);
            }
            
            // Get user with tenant information
            $sql = "SELECT u.*, t.name as tenant_name, t.domain as tenant_domain 
                    FROM users u 
                    JOIN tenants t ON u.tenant_id = t.id 
                    WHERE u.id = :user_id AND u.is_active = 1 AND t.is_active = 1";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':user_id', $user_data['user_id']);
            $stmt->execute();
            
            $user = $stmt->fetch();
            
            if (!$user) {
                errorResponse('المستخدم غير موجود', 404);
            }
            
            $response_data = [
                'user' => [
                    'id' => (string)$user['id'],
                    'firstName' => $user['first_name'],
                    'lastName' => $user['last_name'],
                    'email' => $user['email'],
                    'phone' => $user['phone'],
                    'role' => $user['role'],
                    'createdAt' => $user['created_at'],
                    'updatedAt' => $user['updated_at']
                ],
                'tenant' => [
                    'id' => (string)$user['tenant_id'],
                    'name' => $user['tenant_name'],
                    'domain' => $user['tenant_domain']
                ]
            ];
            
            successResponse($response_data, 'تم جلب بيانات المستخدم بنجاح');
            
        } catch (Exception $e) {
            errorResponse('خطأ في جلب بيانات المستخدم: ' . $e->getMessage(), 500);
        }
    }

    public function logout() {
        // In a real implementation, you would invalidate the token
        successResponse(null, 'تم تسجيل الخروج بنجاح');
    }

    private function generateToken($user_id, $tenant_id) {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode([
            'user_id' => $user_id,
            'tenant_id' => $tenant_id,
            'iat' => time(),
            'exp' => time() + (24 * 60 * 60) // 24 hours
        ]);
        
        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
        
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, JWT_SECRET, true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }

    private function validateToken($token) {
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return false;
        }
        
        list($header, $payload, $signature) = $parts;
        
        $valid_signature = hash_hmac('sha256', $header . "." . $payload, JWT_SECRET, true);
        $valid_signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($valid_signature));
        
        if ($signature !== $valid_signature) {
            return false;
        }
        
        $payload_data = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $payload)), true);
        
        if ($payload_data['exp'] < time()) {
            return false;
        }
        
        return $payload_data;
    }

    private function getTokenFromHeader() {
        $headers = getallheaders();
        if (isset($headers['Authorization'])) {
            $auth_header = $headers['Authorization'];
            if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
                return $matches[1];
            }
        }
        return null;
    }
}

// Handle the request
try {
    $api = new AuthAPI();
    $api->handleRequest();
} catch (Exception $e) {
    errorResponse('خطأ في الخادم: ' . $e->getMessage(), 500);
}
?>
