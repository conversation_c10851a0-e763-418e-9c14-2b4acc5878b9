import { Request, Response } from 'express';
import { ClientService } from '@/services/clientService';
import { CreateClientRequest, UpdateClientRequest, ClientFilters } from '@/types/models';

export class ClientController {
  // إنشاء عميل جديد
  static async createClient(req: Request, res: Response) {
    try {
      if (!req.user || !req.tenant) {
        return res.status(401).json({
          success: false,
          message: 'المصادقة مطلوبة',
          errors: ['AUTHENTICATION_REQUIRED'],
        });
      }

      const data: CreateClientRequest = req.body;

      // التحقق من البيانات المطلوبة
      if (!data.name) {
        return res.status(400).json({
          success: false,
          message: 'اسم العميل مطلوب',
          errors: ['CLIENT_NAME_REQUIRED'],
        });
      }

      // التحقق من صحة البريد الإلكتروني
      if (data.email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.email)) {
          return res.status(400).json({
            success: false,
            message: 'صيغة البريد الإلكتروني غير صحيحة',
            errors: ['INVALID_EMAIL_FORMAT'],
          });
        }
      }

      const result = await ClientService.createClient(req.user.tenantId, data);

      if (!result.success) {
        return res.status(400).json(result);
      }

      return res.status(201).json(result);
    } catch (error) {
      console.error('Create client controller error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ داخلي في الخادم',
        errors: ['INTERNAL_ERROR'],
      });
    }
  }

  // الحصول على جميع العملاء
  static async getClients(req: Request, res: Response) {
    try {
      if (!req.user || !req.tenant) {
        return res.status(401).json({
          success: false,
          message: 'المصادقة مطلوبة',
          errors: ['AUTHENTICATION_REQUIRED'],
        });
      }

      const filters: ClientFilters = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 10,
        search: req.query.search as string,
        isActive: req.query.isActive === 'true' ? true : req.query.isActive === 'false' ? false : undefined,
      };

      const result = await ClientService.getClients(req.user.tenantId, filters);

      if (!result.success) {
        return res.status(400).json(result);
      }

      return res.status(200).json(result);
    } catch (error) {
      console.error('Get clients controller error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ داخلي في الخادم',
        errors: ['INTERNAL_ERROR'],
      });
    }
  }

  // الحصول على عميل واحد
  static async getClientById(req: Request, res: Response) {
    try {
      if (!req.user || !req.tenant) {
        return res.status(401).json({
          success: false,
          message: 'المصادقة مطلوبة',
          errors: ['AUTHENTICATION_REQUIRED'],
        });
      }

      const { clientId } = req.params;

      if (!clientId) {
        return res.status(400).json({
          success: false,
          message: 'معرف العميل مطلوب',
          errors: ['CLIENT_ID_REQUIRED'],
        });
      }

      const result = await ClientService.getClientById(req.user.tenantId, clientId);

      if (!result.success) {
        return res.status(404).json(result);
      }

      return res.status(200).json(result);
    } catch (error) {
      console.error('Get client controller error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ داخلي في الخادم',
        errors: ['INTERNAL_ERROR'],
      });
    }
  }

  // تحديث عميل
  static async updateClient(req: Request, res: Response) {
    try {
      if (!req.user || !req.tenant) {
        return res.status(401).json({
          success: false,
          message: 'المصادقة مطلوبة',
          errors: ['AUTHENTICATION_REQUIRED'],
        });
      }

      const { clientId } = req.params;
      const data: UpdateClientRequest = req.body;

      if (!clientId) {
        return res.status(400).json({
          success: false,
          message: 'معرف العميل مطلوب',
          errors: ['CLIENT_ID_REQUIRED'],
        });
      }

      // التحقق من صحة البريد الإلكتروني إذا تم تقديمه
      if (data.email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.email)) {
          return res.status(400).json({
            success: false,
            message: 'صيغة البريد الإلكتروني غير صحيحة',
            errors: ['INVALID_EMAIL_FORMAT'],
          });
        }
      }

      const result = await ClientService.updateClient(req.user.tenantId, clientId, data);

      if (!result.success) {
        return res.status(400).json(result);
      }

      return res.status(200).json(result);
    } catch (error) {
      console.error('Update client controller error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ داخلي في الخادم',
        errors: ['INTERNAL_ERROR'],
      });
    }
  }

  // حذف عميل
  static async deleteClient(req: Request, res: Response) {
    try {
      if (!req.user || !req.tenant) {
        return res.status(401).json({
          success: false,
          message: 'المصادقة مطلوبة',
          errors: ['AUTHENTICATION_REQUIRED'],
        });
      }

      const { clientId } = req.params;

      if (!clientId) {
        return res.status(400).json({
          success: false,
          message: 'معرف العميل مطلوب',
          errors: ['CLIENT_ID_REQUIRED'],
        });
      }

      const result = await ClientService.deleteClient(req.user.tenantId, clientId);

      if (!result.success) {
        return res.status(400).json(result);
      }

      return res.status(200).json(result);
    } catch (error) {
      console.error('Delete client controller error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ داخلي في الخادم',
        errors: ['INTERNAL_ERROR'],
      });
    }
  }

  // الحصول على إحصائيات العميل
  static async getClientStats(req: Request, res: Response) {
    try {
      if (!req.user || !req.tenant) {
        return res.status(401).json({
          success: false,
          message: 'المصادقة مطلوبة',
          errors: ['AUTHENTICATION_REQUIRED'],
        });
      }

      const { clientId } = req.params;

      if (!clientId) {
        return res.status(400).json({
          success: false,
          message: 'معرف العميل مطلوب',
          errors: ['CLIENT_ID_REQUIRED'],
        });
      }

      const result = await ClientService.getClientStats(req.user.tenantId, clientId);

      if (!result.success) {
        return res.status(404).json(result);
      }

      return res.status(200).json(result);
    } catch (error) {
      console.error('Get client stats controller error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ داخلي في الخادم',
        errors: ['INTERNAL_ERROR'],
      });
    }
  }

  // البحث في العملاء
  static async searchClients(req: Request, res: Response) {
    try {
      if (!req.user || !req.tenant) {
        return res.status(401).json({
          success: false,
          message: 'المصادقة مطلوبة',
          errors: ['AUTHENTICATION_REQUIRED'],
        });
      }

      const { q } = req.query;

      if (!q || typeof q !== 'string') {
        return res.status(400).json({
          success: false,
          message: 'نص البحث مطلوب',
          errors: ['SEARCH_QUERY_REQUIRED'],
        });
      }

      const result = await ClientService.searchClients(req.user.tenantId, q);

      if (!result.success) {
        return res.status(400).json(result);
      }

      return res.status(200).json(result);
    } catch (error) {
      console.error('Search clients controller error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ داخلي في الخادم',
        errors: ['INTERNAL_ERROR'],
      });
    }
  }
}
