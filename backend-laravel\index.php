<?php
require_once 'config/database.php';

setCorsHeaders();

// Simple router
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);
$path_parts = explode('/', trim($path, '/'));

// Remove 'backend-laravel' from path if present
if (isset($path_parts[0]) && $path_parts[0] === 'backend-laravel') {
    array_shift($path_parts);
}

// Check if it's an API request
if (isset($path_parts[0]) && $path_parts[0] === 'api') {
    if (isset($path_parts[1])) {
        $endpoint = $path_parts[1];
        
        switch ($endpoint) {
            case 'auth':
                require_once 'api/auth.php';
                break;
            case 'clients':
                require_once 'api/clients.php';
                break;
            case 'dashboard':
                require_once 'api/dashboard.php';
                break;
            case 'invoices':
                require_once 'api/invoices.php';
                break;
            default:
                errorResponse('نقطة نهاية غير موجودة', 404);
        }
    } else {
        errorResponse('نقطة نهاية مطلوبة', 400);
    }
} else {
    // API documentation or welcome page
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Invoice Management API</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
                direction: rtl;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h1 {
                color: #333;
                text-align: center;
                margin-bottom: 30px;
            }
            .endpoint {
                background: #f8f9fa;
                padding: 15px;
                margin: 10px 0;
                border-radius: 5px;
                border-left: 4px solid #007bff;
            }
            .method {
                display: inline-block;
                padding: 2px 8px;
                border-radius: 3px;
                color: white;
                font-weight: bold;
                margin-left: 10px;
            }
            .get { background-color: #28a745; }
            .post { background-color: #007bff; }
            .put { background-color: #ffc107; color: #000; }
            .delete { background-color: #dc3545; }
            .status {
                text-align: center;
                padding: 20px;
                background: #d4edda;
                border: 1px solid #c3e6cb;
                border-radius: 5px;
                color: #155724;
                margin-bottom: 20px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="status">
                ✅ API يعمل بنجاح - Invoice Management System
            </div>
            
            <h1>نظام إدارة الفواتير - API Documentation</h1>
            
            <h2>🔐 المصادقة (Authentication)</h2>
            <div class="endpoint">
                <span class="method post">POST</span>
                <strong>/api/auth/login</strong> - تسجيل الدخول
            </div>
            <div class="endpoint">
                <span class="method post">POST</span>
                <strong>/api/auth/register</strong> - إنشاء حساب جديد
            </div>
            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/auth/profile</strong> - جلب بيانات المستخدم
            </div>
            <div class="endpoint">
                <span class="method post">POST</span>
                <strong>/api/auth/logout</strong> - تسجيل الخروج
            </div>
            
            <h2>👥 إدارة العملاء (Clients)</h2>
            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/clients</strong> - جلب قائمة العملاء
            </div>
            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/clients/{id}</strong> - جلب عميل محدد
            </div>
            <div class="endpoint">
                <span class="method post">POST</span>
                <strong>/api/clients</strong> - إضافة عميل جديد
            </div>
            <div class="endpoint">
                <span class="method put">PUT</span>
                <strong>/api/clients/{id}</strong> - تحديث عميل
            </div>
            <div class="endpoint">
                <span class="method delete">DELETE</span>
                <strong>/api/clients/{id}</strong> - حذف عميل
            </div>
            
            <h2>📄 إدارة الفواتير (Invoices)</h2>
            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/invoices</strong> - جلب قائمة الفواتير
            </div>
            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/invoices/{id}</strong> - جلب فاتورة محددة
            </div>
            <div class="endpoint">
                <span class="method post">POST</span>
                <strong>/api/invoices</strong> - إنشاء فاتورة جديدة
            </div>
            <div class="endpoint">
                <span class="method put">PUT</span>
                <strong>/api/invoices/{id}</strong> - تحديث فاتورة
            </div>
            <div class="endpoint">
                <span class="method delete">DELETE</span>
                <strong>/api/invoices/{id}</strong> - حذف فاتورة
            </div>
            
            <h2>📊 لوحة التحكم (Dashboard)</h2>
            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/dashboard/stats</strong> - إحصائيات لوحة التحكم
            </div>
            
            <h2>🔧 معلومات قاعدة البيانات</h2>
            <p><strong>اسم قاعدة البيانات:</strong> invoice_management</p>
            <p><strong>الخادم:</strong> localhost</p>
            <p><strong>المستخدم:</strong> root</p>
            
            <h2>📝 بيانات تجريبية للاختبار</h2>
            <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
            <p><strong>كلمة المرور:</strong> admin123</p>
            
            <h2>🚀 كيفية الاستخدام</h2>
            <ol>
                <li>تأكد من تشغيل XAMPP وتفعيل MySQL</li>
                <li>قم بإنشاء قاعدة البيانات باستخدام ملف migrations.sql</li>
                <li>استخدم API endpoints المذكورة أعلاه</li>
                <li>تأكد من إرسال Authorization header مع JWT token للطلبات المحمية</li>
            </ol>
            
            <div style="text-align: center; margin-top: 30px; color: #666;">
                <p>تم تطوير هذا النظام بعناية لإدارة الفواتير بكفاءة</p>
                <p>© 2024 Invoice Management System</p>
            </div>
        </div>
    </body>
    </html>
    <?php
}
?>
