import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { toast } from 'react-hot-toast';
import Cookies from 'js-cookie';
import type {
  ApiResponse,
  AuthResponse,
  LoginRequest,
  RegisterRequest,
  User,
  Client,
  ClientFilters,
  CreateClientRequest,
  UpdateClientRequest,
  Invoice,
  InvoiceFilters,
  InvoiceWithDetails,
  CreateInvoiceRequest,
  UpdateInvoiceRequest,
  DashboardStats,
  MonthlySalesData,
  InvoiceStatusStats,
} from '@/types';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';
const TOKEN_KEY = 'auth_token';
const REFRESH_TOKEN_KEY = 'refresh_token';

// Create axios instance
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: API_BASE_URL,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  });

  // Request interceptor to add auth token
  client.interceptors.request.use(
    (config) => {
      const token = Cookies.get(TOKEN_KEY);
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor for error handling
  client.interceptors.response.use(
    (response: AxiosResponse) => {
      return response;
    },
    async (error) => {
      const originalRequest = error.config;

      // Handle 401 errors (unauthorized)
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;

        try {
          const refreshToken = Cookies.get(REFRESH_TOKEN_KEY);
          if (refreshToken) {
            const response = await axios.post(`${API_BASE_URL}/api/auth/refresh`, {
              refreshToken,
            });

            if (response.data.success) {
              const { token } = response.data.data;
              Cookies.set(TOKEN_KEY, token, { expires: 7 });
              originalRequest.headers.Authorization = `Bearer ${token}`;
              return client(originalRequest);
            }
          }
        } catch (refreshError) {
          // Refresh failed, redirect to login
          Cookies.remove(TOKEN_KEY);
          Cookies.remove(REFRESH_TOKEN_KEY);
          window.location.href = '/auth/login';
          return Promise.reject(refreshError);
        }
      }

      // Handle other errors
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else if (error.message) {
        toast.error(error.message);
      } else {
        toast.error('حدث خطأ غير متوقع');
      }

      return Promise.reject(error);
    }
  );

  return client;
};

// Create API client instance
const apiClient = createApiClient();

// API Helper Functions
const handleApiResponse = <T>(response: AxiosResponse<ApiResponse<T>>): ApiResponse<T> => {
  return response.data;
};

const handleApiError = (error: any): never => {
  if (error.response?.data) {
    throw error.response.data;
  }
  throw {
    success: false,
    message: error.message || 'حدث خطأ غير متوقع',
    errors: ['NETWORK_ERROR'],
  };
};

// Auth API
export const authApi = {
  // Login
  async login(data: LoginRequest): Promise<AuthResponse> {
    try {
      const response = await apiClient.post<AuthResponse>('/api/auth/login', data);
      const result = handleApiResponse(response);
      
      if (result.success && result.data) {
        // Store tokens
        Cookies.set(TOKEN_KEY, result.data.token, { expires: 7 });
        Cookies.set(REFRESH_TOKEN_KEY, result.data.refreshToken, { expires: 30 });
        toast.success(result.message);
      }
      
      return result;
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Register
  async register(data: RegisterRequest): Promise<AuthResponse> {
    try {
      const response = await apiClient.post<AuthResponse>('/api/auth/register', data);
      const result = handleApiResponse(response);
      
      if (result.success) {
        toast.success(result.message);
      }
      
      return result;
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Get current user profile
  async getProfile(): Promise<ApiResponse<User>> {
    try {
      const response = await apiClient.get<ApiResponse<User>>('/api/auth/profile');
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Logout
  async logout(): Promise<void> {
    try {
      await apiClient.post('/api/auth/logout');
    } catch (error) {
      // Ignore logout errors
    } finally {
      // Always clear tokens
      Cookies.remove(TOKEN_KEY);
      Cookies.remove(REFRESH_TOKEN_KEY);
      window.location.href = '/auth/login';
    }
  },

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!Cookies.get(TOKEN_KEY);
  },

  // Get stored token
  getToken(): string | undefined {
    return Cookies.get(TOKEN_KEY);
  },
};

// Clients API
export const clientsApi = {
  // Get all clients
  async getClients(filters?: ClientFilters): Promise<ApiResponse<Client[]>> {
    try {
      const response = await apiClient.get<ApiResponse<Client[]>>('/api/clients', {
        params: filters,
      });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Get client by ID
  async getClient(id: string): Promise<ApiResponse<Client>> {
    try {
      const response = await apiClient.get<ApiResponse<Client>>(`/api/clients/${id}`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Create new client
  async createClient(data: CreateClientRequest): Promise<ApiResponse<Client>> {
    try {
      const response = await apiClient.post<ApiResponse<Client>>('/api/clients', data);
      const result = handleApiResponse(response);
      
      if (result.success) {
        toast.success(result.message);
      }
      
      return result;
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Update client
  async updateClient(id: string, data: UpdateClientRequest): Promise<ApiResponse<Client>> {
    try {
      const response = await apiClient.put<ApiResponse<Client>>(`/api/clients/${id}`, data);
      const result = handleApiResponse(response);
      
      if (result.success) {
        toast.success(result.message);
      }
      
      return result;
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Delete client
  async deleteClient(id: string): Promise<ApiResponse<void>> {
    try {
      const response = await apiClient.delete<ApiResponse<void>>(`/api/clients/${id}`);
      const result = handleApiResponse(response);
      
      if (result.success) {
        toast.success(result.message);
      }
      
      return result;
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Search clients
  async searchClients(query: string): Promise<ApiResponse<Client[]>> {
    try {
      const response = await apiClient.get<ApiResponse<Client[]>>('/api/clients/search', {
        params: { q: query },
      });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error);
    }
  },
};

// Invoices API
export const invoicesApi = {
  // Get all invoices
  async getInvoices(filters?: InvoiceFilters): Promise<ApiResponse<InvoiceWithDetails[]>> {
    try {
      const response = await apiClient.get<ApiResponse<InvoiceWithDetails[]>>('/api/invoices', {
        params: filters,
      });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Get invoice by ID
  async getInvoice(id: string): Promise<ApiResponse<InvoiceWithDetails>> {
    try {
      const response = await apiClient.get<ApiResponse<InvoiceWithDetails>>(`/api/invoices/${id}`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Create new invoice
  async createInvoice(data: CreateInvoiceRequest): Promise<ApiResponse<InvoiceWithDetails>> {
    try {
      const response = await apiClient.post<ApiResponse<InvoiceWithDetails>>('/api/invoices', data);
      const result = handleApiResponse(response);
      
      if (result.success) {
        toast.success(result.message);
      }
      
      return result;
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Update invoice
  async updateInvoice(id: string, data: UpdateInvoiceRequest): Promise<ApiResponse<InvoiceWithDetails>> {
    try {
      const response = await apiClient.put<ApiResponse<InvoiceWithDetails>>(`/api/invoices/${id}`, data);
      const result = handleApiResponse(response);
      
      if (result.success) {
        toast.success(result.message);
      }
      
      return result;
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Delete invoice
  async deleteInvoice(id: string): Promise<ApiResponse<void>> {
    try {
      const response = await apiClient.delete<ApiResponse<void>>(`/api/invoices/${id}`);
      const result = handleApiResponse(response);
      
      if (result.success) {
        toast.success(result.message);
      }
      
      return result;
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Send invoice
  async sendInvoice(id: string): Promise<ApiResponse<InvoiceWithDetails>> {
    try {
      const response = await apiClient.post<ApiResponse<InvoiceWithDetails>>(`/api/invoices/${id}/send`);
      const result = handleApiResponse(response);
      
      if (result.success) {
        toast.success(result.message);
      }
      
      return result;
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Update invoice status
  async updateInvoiceStatus(id: string, status: string): Promise<ApiResponse<InvoiceWithDetails>> {
    try {
      const response = await apiClient.patch<ApiResponse<InvoiceWithDetails>>(`/api/invoices/${id}/status`, {
        status,
      });
      const result = handleApiResponse(response);
      
      if (result.success) {
        toast.success(result.message);
      }
      
      return result;
    } catch (error) {
      return handleApiError(error);
    }
  },
};

// Dashboard API
export const dashboardApi = {
  // Get dashboard stats
  async getStats(): Promise<ApiResponse<DashboardStats>> {
    try {
      const response = await apiClient.get<ApiResponse<DashboardStats>>('/api/dashboard/stats');
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Get monthly sales stats
  async getMonthlySales(year?: number): Promise<ApiResponse<MonthlySalesData[]>> {
    try {
      const response = await apiClient.get<ApiResponse<MonthlySalesData[]>>('/api/dashboard/sales/monthly', {
        params: year ? { year } : {},
      });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Get invoice status stats
  async getInvoiceStatusStats(): Promise<ApiResponse<InvoiceStatusStats[]>> {
    try {
      const response = await apiClient.get<ApiResponse<InvoiceStatusStats[]>>('/api/dashboard/invoices/status-stats');
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Get overdue invoices
  async getOverdueInvoices(): Promise<ApiResponse<InvoiceWithDetails[]>> {
    try {
      const response = await apiClient.get<ApiResponse<InvoiceWithDetails[]>>('/api/dashboard/invoices/overdue');
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error);
    }
  },
};

// Health check
export const healthApi = {
  async check(): Promise<ApiResponse<any>> {
    try {
      const response = await apiClient.get<ApiResponse<any>>('/health');
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error);
    }
  },
};

// Export the main API client for custom requests
export { apiClient };

// Export all APIs
export default {
  auth: authApi,
  clients: clientsApi,
  invoices: invoicesApi,
  dashboard: dashboardApi,
  health: healthApi,
};
