import { Router } from 'express';
import { InvoiceController } from '@/controllers/invoiceController';
import { authenticate, authorize } from '@/middleware/auth';

const router = Router();

// جميع المسارات تتطلب مصادقة
router.use(authenticate);

// الحصول على جميع الفواتير
router.get('/', 
  authorize(['invoices:read']),
  InvoiceController.getInvoices
);

// الحصول على فاتورة واحدة
router.get('/:invoiceId', 
  authorize(['invoices:read']),
  InvoiceController.getInvoiceById
);

// إنشاء فاتورة جديدة
router.post('/', 
  authorize(['invoices:write']),
  InvoiceController.createInvoice
);

// تحديث فاتورة
router.put('/:invoiceId', 
  authorize(['invoices:write']),
  InvoiceController.updateInvoice
);

// تحديث حالة الفاتورة
router.patch('/:invoiceId/status', 
  authorize(['invoices:write']),
  InvoiceController.updateInvoiceStatus
);

// إرسال فاتورة
router.post('/:invoiceId/send', 
  authorize(['invoices:send']),
  InvoiceController.sendInvoice
);

// حذف فاتورة
router.delete('/:invoiceId', 
  authorize(['invoices:delete']),
  InvoiceController.deleteInvoice
);

export default router;
